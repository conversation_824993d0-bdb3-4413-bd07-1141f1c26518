imports:
  root: ../../__package__.yml
service:
  auth: false
  base-path: ''
  endpoints:
    create:
      path: /v1/dubbing/resource/{dubbing_id}/speaker/{speaker_id}/segment
      method: POST
      auth: false
      docs: >-
        Creates a new segment in dubbing resource with a start and end time for
        the speaker in every available language. Does not automatically generate
        transcripts/translations/audio.
      source:
        openapi: openapi.json
      path-parameters:
        dubbing_id:
          type: string
          docs: ID of the dubbing project.
        speaker_id:
          type: string
          docs: ID of the speaker.
      display-name: Add speaker segment to dubbing resource
      request:
        name: SegmentCreatePayload
        body:
          properties:
            start_time: double
            end_time: double
            text:
              type: optional<string>
            translations:
              type: optional<map<string, optional<string>>>
        content-type: application/json
      response:
        docs: Successful Response
        type: root.SegmentCreateResponse
        status-code: 201
      errors:
        - root.UnprocessableEntityError
      examples:
        - path-parameters:
            dubbing_id: dubbing_id
            speaker_id: speaker_id
          request:
            start_time: 1.1
            end_time: 1.1
          response:
            body:
              version: 1
              new_segment: new_segment
  source:
    openapi: openapi.json
