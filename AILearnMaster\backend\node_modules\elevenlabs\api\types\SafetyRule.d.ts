/**
 * This file was auto-generated by Fern from our API Definition.
 */
export type SafetyRule = "sexual_minors" | "forget_moderation" | "extremism" | "scam_fraud" | "political" | "self_harm" | "illegal_distribution_medical" | "sexual_adults" | "unknown";
export declare const SafetyRule: {
    readonly SexualMinors: "sexual_minors";
    readonly ForgetModeration: "forget_moderation";
    readonly Extremism: "extremism";
    readonly <PERSON>: "scam_fraud";
    readonly Political: "political";
    readonly SelfHarm: "self_harm";
    readonly IllegalDistributionMedical: "illegal_distribution_medical";
    readonly SexualAdults: "sexual_adults";
    readonly Unknown: "unknown";
};
