{"include": ["server/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts", "../shared/**/*"], "compilerOptions": {"incremental": true, "tsBuildInfoFile": "./node_modules/typescript/tsbuildinfo", "noEmit": true, "module": "ESNext", "strict": true, "lib": ["esnext"], "esModuleInterop": true, "skipLibCheck": true, "allowImportingTsExtensions": true, "moduleResolution": "bundler", "baseUrl": ".", "types": ["node"], "downlevelIteration": true, "target": "es2020", "paths": {"@shared/*": ["../shared/*"]}}}