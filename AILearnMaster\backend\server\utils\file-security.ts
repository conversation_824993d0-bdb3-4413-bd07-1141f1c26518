/**
 * File Security Utilities
 * Comprehensive file validation and sanitization
 */

import * as path from 'path';
import * as crypto from 'crypto';

/**
 * Allowed file types with their MIME types and extensions
 */
export const ALLOWED_FILE_TYPES = {
  images: {
    mimeTypes: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'],
    extensions: ['.jpg', '.jpeg', '.png', '.gif', '.webp']
  },
  documents: {
    mimeTypes: ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
    extensions: ['.pdf', '.doc', '.docx']
  },
  audio: {
    mimeTypes: ['audio/mpeg', 'audio/wav', 'audio/mp3', 'audio/ogg'],
    extensions: ['.mp3', '.wav', '.ogg']
  },
  video: {
    mimeTypes: ['video/mp4', 'video/mpeg', 'video/quicktime', 'video/webm'],
    extensions: ['.mp4', '.mpeg', '.mov', '.webm']
  }
};

/**
 * Maximum file sizes by category (in bytes)
 */
export const MAX_FILE_SIZES = {
  images: 5 * 1024 * 1024,    // 5MB
  documents: 10 * 1024 * 1024, // 10MB
  audio: 20 * 1024 * 1024,     // 20MB
  video: 100 * 1024 * 1024     // 100MB
};

/**
 * Sanitize filename to prevent path traversal and other attacks
 */
export const sanitizeFilename = (filename: string): string => {
  if (!filename) {
    return 'unnamed_file';
  }

  // Remove path separators and dangerous characters
  let sanitized = filename
    .replace(/[\/\\:*?"<>|]/g, '_')  // Replace dangerous characters
    .replace(/\.\./g, '_')           // Remove path traversal attempts
    .replace(/^\.+/, '')             // Remove leading dots
    .replace(/\.+$/, '')             // Remove trailing dots
    .replace(/\s+/g, '_')            // Replace spaces with underscores
    .replace(/_+/g, '_')             // Replace multiple underscores with single
    .toLowerCase();

  // Ensure filename is not empty after sanitization
  if (!sanitized || sanitized === '_') {
    sanitized = 'unnamed_file';
  }

  // Limit filename length (excluding extension)
  const ext = path.extname(sanitized);
  const nameWithoutExt = path.basename(sanitized, ext);
  
  if (nameWithoutExt.length > 50) {
    sanitized = nameWithoutExt.substring(0, 50) + ext;
  }

  return sanitized;
};

/**
 * Generate secure unique filename
 */
export const generateSecureFilename = (originalFilename: string, prefix?: string): string => {
  const sanitizedOriginal = sanitizeFilename(originalFilename);
  const ext = path.extname(sanitizedOriginal);
  const timestamp = Date.now();
  const randomBytes = crypto.randomBytes(8).toString('hex');
  
  const prefixPart = prefix ? `${prefix}_` : '';
  const namePart = path.basename(sanitizedOriginal, ext).substring(0, 20); // Limit name part
  
  return `${prefixPart}${namePart}_${timestamp}_${randomBytes}${ext}`;
};

/**
 * Validate file type and size
 */
export const validateFile = (file: Express.Multer.File, allowedCategory: keyof typeof ALLOWED_FILE_TYPES): {
  isValid: boolean;
  error?: string;
} => {
  if (!file) {
    return { isValid: false, error: 'No file provided' };
  }

  const allowedTypes = ALLOWED_FILE_TYPES[allowedCategory];
  const maxSize = MAX_FILE_SIZES[allowedCategory];

  // Check file size
  if (file.size > maxSize) {
    return { 
      isValid: false, 
      error: `File size exceeds ${Math.round(maxSize / (1024 * 1024))}MB limit` 
    };
  }

  // Check MIME type
  if (!allowedTypes.mimeTypes.includes(file.mimetype)) {
    return { 
      isValid: false, 
      error: `File type ${file.mimetype} not allowed for ${allowedCategory}` 
    };
  }

  // Check file extension
  const fileExtension = path.extname(file.originalname).toLowerCase();
  if (!allowedTypes.extensions.includes(fileExtension)) {
    return { 
      isValid: false, 
      error: `File extension ${fileExtension} not allowed for ${allowedCategory}` 
    };
  }

  // Additional security checks
  if (file.originalname.includes('..') || file.originalname.includes('/') || file.originalname.includes('\\')) {
    return { 
      isValid: false, 
      error: 'Invalid characters in filename' 
    };
  }

  return { isValid: true };
};

/**
 * Scan file content for malicious patterns (basic implementation)
 */
export const scanFileContent = async (filePath: string): Promise<{
  isSafe: boolean;
  threats?: string[];
}> => {
  // This is a basic implementation. In production, you'd want to use
  // a proper antivirus scanner or malware detection service
  
  const fs = await import('fs');
  
  try {
    // Read first 1KB of file to check for suspicious patterns
    const buffer = Buffer.alloc(1024);
    const fd = fs.openSync(filePath, 'r');
    const bytesRead = fs.readSync(fd, buffer, 0, 1024, 0);
    fs.closeSync(fd);
    
    const content = buffer.toString('utf8', 0, bytesRead);
    
    // Check for suspicious patterns
    const suspiciousPatterns = [
      /<script/i,           // JavaScript
      /javascript:/i,       // JavaScript protocol
      /vbscript:/i,         // VBScript
      /on\w+\s*=/i,        // Event handlers
      /eval\s*\(/i,        // eval function
      /exec\s*\(/i,        // exec function
      /system\s*\(/i,      // system calls
      /\$\{.*\}/,          // Template literals
      /<%.*%>/,            // Server-side includes
    ];
    
    const threats: string[] = [];
    
    for (const pattern of suspiciousPatterns) {
      if (pattern.test(content)) {
        threats.push(`Suspicious pattern detected: ${pattern.source}`);
      }
    }
    
    return {
      isSafe: threats.length === 0,
      threats: threats.length > 0 ? threats : undefined
    };
    
  } catch (error) {
    console.error('File content scan error:', error);
    return {
      isSafe: false,
      threats: ['Unable to scan file content']
    };
  }
};

/**
 * Create secure multer configuration
 */
export const createSecureMulterConfig = (
  category: keyof typeof ALLOWED_FILE_TYPES,
  uploadPath: string = 'uploads/'
) => {
  return {
    dest: uploadPath,
    limits: { 
      fileSize: MAX_FILE_SIZES[category],
      files: 5 // Maximum 5 files per request
    },
    fileFilter: (req: any, file: Express.Multer.File, cb: any) => {
      const validation = validateFile(file, category);
      
      if (!validation.isValid) {
        return cb(new Error(validation.error), false);
      }
      
      cb(null, true);
    },
    filename: (req: any, file: Express.Multer.File, cb: any) => {
      const secureFilename = generateSecureFilename(file.originalname);
      cb(null, secureFilename);
    }
  };
};

/**
 * Virus scanning placeholder (integrate with actual antivirus service)
 */
export const performVirusScan = async (filePath: string): Promise<{
  isClean: boolean;
  threats?: string[];
}> => {
  // Placeholder for actual virus scanning
  // In production, integrate with services like:
  // - ClamAV
  // - VirusTotal API
  // - AWS GuardDuty
  // - Azure Defender
  
  console.log(`[SECURITY] Virus scan placeholder for: ${filePath}`);
  
  // For now, just perform basic content scanning
  const contentScan = await scanFileContent(filePath);
  
  return {
    isClean: contentScan.isSafe,
    threats: contentScan.threats
  };
};