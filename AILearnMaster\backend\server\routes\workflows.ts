/**
 * Complete Course Creation Workflows
 * Implements both Traditional and Avatar course creation flows
 */

import { Router, Request, Response } from 'express';
import { z } from 'zod';
import * as mistralModalService from '../services/mistralModalService';
import { enhancedTraditionalCourseGenerator } from '../services/enhanced-traditional-course-generator';
import { enhancedAvatarCourseGenerator } from '../services/enhanced-avatar-course-generator';
import { unifiedCourseGenerationService } from '../services/unified-course-generation-service';
import { VoiceServiceManager } from '../services/voice-service-manager';

// Initialize voice service manager
const voiceServiceManager = new VoiceServiceManager();
import multer from 'multer';
import path from 'path';
import fs from 'fs';

const router = Router();

// Configure multer for avatar image uploads
const upload = multer({
  dest: 'uploads/avatars/',
  limits: { fileSize: 10 * 1024 * 1024 }, // 10MB limit
  fileFilter: (req, file, cb) => {
    const allowedTypes = /jpeg|jpg|png|gif|webp/;
    const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
    const mimetype = allowedTypes.test(file.mimetype);
    
    if (mimetype && extname) {
      return cb(null, true);
    } else {
      cb(new Error('Only image files are allowed'));
    }
  }
});

// Input validation schemas
const traditionaWorkflowSchema = z.object({
  title: z.string().min(1, 'Course title is required'),
  category: z.string().optional().default('general'),
  difficulty: z.enum(['beginner', 'intermediate', 'advanced']).optional().default('intermediate'),
  duration: z.enum(['short', 'medium', 'long']).optional().default('medium'),
  voiceId: z.string().optional(),
  voiceProvider: z.enum(['coqui', 'openai', 'elevenlabs']).optional().default('coqui')
});

const avatarWorkflowSchema = z.object({
  title: z.string().min(1, 'Course title is required'),
  category: z.string().optional().default('general'),
  difficulty: z.enum(['beginner', 'intermediate', 'advanced']).optional().default('intermediate'),
  duration: z.enum(['short', 'medium', 'long']).optional().default('medium'),
  voiceId: z.string().optional(),
  voiceProvider: z.enum(['coqui', 'openai', 'elevenlabs']).optional().default('coqui'),
  avatarImagePath: z.string().optional() // Will be set after upload
});

// Progress tracking for workflows
const workflowProgress = new Map<string, {
  status: 'running' | 'completed' | 'failed';
  currentStep: string;
  totalSteps: number;
  completedSteps: number;
  results: any;
  error?: string;
  startTime: Date;
}>();

/**
 * 1. TRADITIONAL COURSE CREATION WORKFLOW
 * Input: Course title only
 * Output: Complete video-based course with AI narration and slides
 */
router.post('/traditional-course', async (req: Request, res: Response) => {
  try {
    const validatedData = traditionaWorkflowSchema.parse(req.body);
    const workflowId = `traditional_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    // Initialize progress tracking
    workflowProgress.set(workflowId, {
      status: 'running',
      currentStep: 'Initializing',
      totalSteps: 7,
      completedSteps: 0,
      results: {},
      startTime: new Date()
    });

    // Start async workflow
    processTraditionalWorkflow(workflowId, validatedData);

    res.json({
      success: true,
      workflowId,
      message: 'Traditional course creation workflow started',
      statusUrl: `/api/workflows/status/${workflowId}`
    });

  } catch (error) {
    console.error('Traditional workflow error:', error);
    res.status(400).json({
      success: false,
      error: error instanceof z.ZodError ? error.errors : 'Invalid input data'
    });
  }
});

/**
 * 2A. AVATAR COURSE CREATION WORKFLOW (JSON Only - for testing)
 * Input: Course title + avatar metadata
 * Output: Complete avatar-based course with talking head videos
 */
router.post('/avatar-course-json', async (req: Request, res: Response) => {
  try {
    const validatedData = avatarWorkflowSchema.parse(req.body);
    const workflowId = `avatar_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    // Initialize progress tracking
    workflowProgress.set(workflowId, {
      status: 'running',
      currentStep: 'Initializing',
      totalSteps: 8,
      completedSteps: 0,
      startTime: new Date(),
      results: {
        courseStructure: null,
        lessonScripts: [],
        audioFiles: [],
        avatarVideos: [],
        finalCourse: null
      }
    });

    // Start the workflow
    processAvatarWorkflow(workflowId, validatedData).catch(error => {
      console.error(`Avatar workflow ${workflowId} failed:`, error);
      workflowProgress.set(workflowId, {
        ...workflowProgress.get(workflowId)!,
        status: 'failed',
        error: error.message
      });
    });

    res.json({
      success: true,
      workflowId,
      message: 'Avatar course creation workflow started',
      statusUrl: `/api/workflows/status/${workflowId}`
    });
  } catch (error) {
    console.error('Avatar workflow error:', error);
    res.status(400).json({
      success: false,
      error: error instanceof z.ZodError ? error.errors : 'Invalid input data'
    });
  }
});

/**
 * 2B. AVATAR COURSE CREATION WORKFLOW (With File Upload)
 * Input: Course title + avatar image
 * Output: Complete avatar-based course with talking head videos
 */
router.post('/avatar-course', upload.single('avatarImage'), async (req: Request, res: Response) => {
  try {
    console.log('Avatar workflow request:', {
      body: req.body,
      file: req.file ? { filename: req.file.filename, originalname: req.file.originalname } : null,
      hasData: !!req.body.data
    });
    
    // Handle both JSON and multipart form data
    let body: any;
    if (req.body.data) {
      // Multipart form data (with file upload)
      try {
        body = JSON.parse(req.body.data);
      } catch (e) {
        body = req.body;
      }
    } else if (Object.keys(req.body).length > 0) {
      // Regular JSON body (for testing without file)
      body = req.body;
    } else {
      // Fallback with default values for testing
      body = {
        title: req.body.title || "Avatar Course Test",
        description: req.body.description || "Testing avatar workflow",
        category: req.body.category || "technology",
        difficulty: req.body.difficulty || "beginner",
        voiceProvider: req.body.voiceProvider || "openai",
        voiceId: req.body.voiceId || "alloy"
      };
    }
    
    console.log('Parsed body for validation:', body);
    const validatedData = avatarWorkflowSchema.parse(body);
    
    if (req.file) {
      validatedData.avatarImagePath = req.file.path;
    }
    
    const workflowId = `avatar_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    // Initialize progress tracking
    workflowProgress.set(workflowId, {
      status: 'running',
      currentStep: 'Initializing',
      totalSteps: 8,
      completedSteps: 0,
      results: {},
      startTime: new Date()
    });

    // Start async workflow
    processAvatarWorkflow(workflowId, validatedData);

    res.json({
      success: true,
      workflowId,
      message: 'Avatar course creation workflow started',
      statusUrl: `/api/workflows/status/${workflowId}`
    });

  } catch (error) {
    console.error('Avatar workflow error:', error);
    res.status(400).json({
      success: false,
      error: error instanceof z.ZodError ? error.errors : 'Invalid input data'
    });
  }
});

/**
 * Get workflow status and progress
 */
router.get('/status/:workflowId', (req: Request, res: Response) => {
  const { workflowId } = req.params;
  const progress = workflowProgress.get(workflowId);
  
  if (!progress) {
    return res.status(404).json({
      success: false,
      error: 'Workflow not found'
    });
  }
  
  const duration = Date.now() - progress.startTime.getTime();
  
  res.json({
    success: true,
    workflowId,
    status: progress.status,
    currentStep: progress.currentStep,
    progress: {
      completed: progress.completedSteps,
      total: progress.totalSteps,
      percentage: Math.round((progress.completedSteps / progress.totalSteps) * 100)
    },
    duration: Math.round(duration / 1000), // seconds
    results: progress.results,
    error: progress.error
  });
});

/**
 * Get available voice options
 */
router.get('/voices/:provider', async (req: Request, res: Response) => {
  try {
    const { provider } = req.params;
    
    let voices = [];
    switch (provider) {
      case 'coqui':
        voices = [
          { id: 'coqui_female_1', name: 'Professional Female', language: 'en', style: 'professional' },
          { id: 'coqui_male_1', name: 'Professional Male', language: 'en', style: 'professional' },
          { id: 'coqui_female_2', name: 'Friendly Female', language: 'en', style: 'friendly' },
          { id: 'coqui_male_2', name: 'Friendly Male', language: 'en', style: 'friendly' }
        ];
        break;
      case 'openai':
        voices = [
          { id: 'alloy', name: 'Alloy', language: 'en', style: 'neutral' },
          { id: 'echo', name: 'Echo', language: 'en', style: 'professional' },
          { id: 'fable', name: 'Fable', language: 'en', style: 'warm' },
          { id: 'onyx', name: 'Onyx', language: 'en', style: 'deep' },
          { id: 'nova', name: 'Nova', language: 'en', style: 'bright' },
          { id: 'shimmer', name: 'Shimmer', language: 'en', style: 'gentle' }
        ];
        break;
      case 'elevenlabs':
        voices = [
          { id: 'rachel', name: 'Rachel', language: 'en', style: 'professional' },
          { id: 'domi', name: 'Domi', language: 'en', style: 'confident' },
          { id: 'bella', name: 'Bella', language: 'en', style: 'friendly' },
          { id: 'antoni', name: 'Antoni', language: 'en', style: 'warm' }
        ];
        break;
      default:
        return res.status(400).json({ error: 'Invalid voice provider' });
    }
    
    res.json({ success: true, voices });
  } catch (error) {
    console.error('Error fetching voices:', error);
    res.status(500).json({ error: 'Failed to fetch voices' });
  }
});

/**
 * TRADITIONAL COURSE WORKFLOW IMPLEMENTATION
 */
async function processTraditionalWorkflow(workflowId: string, data: z.infer<typeof traditionaWorkflowSchema>) {
  const progress = workflowProgress.get(workflowId)!;
  
  try {
    // Step 1: Generate course content using Mistral LLM
    updateProgress(workflowId, 'Generating course structure and content', 1);
    
    const courseStructure = await mistralModalService.generateCourseStructure({
      title: data.title,
      category: data.category,
      description: `Comprehensive ${data.title} course`,
      targetAudience: `${data.difficulty} level learners`,
      useAI: true
    });
    
    progress.results.courseStructure = courseStructure;
    
    // Step 2: Generate detailed scripts for all lessons
    updateProgress(workflowId, 'Generating lesson scripts', 2);
    
    const scripts = [];
    for (const module of courseStructure.modules || []) {
      for (const lesson of module.lessons || []) {
        // Generate lesson script using Mistral AI
        let script = '';
        try {
          const scriptResult = await mistralModalService.generateLessonScripts({
            courseTitle: data.title,
            moduleTitle: module.title,
            lessonTitle: lesson.title,
            lessonDescription: lesson.description || `Comprehensive content for ${lesson.title}`,
            duration: '10 min',
            voiceStyle: 'professional'
          });
          script = scriptResult || `Professional lesson content for ${lesson.title}. This lesson covers the fundamentals and key concepts needed to understand ${lesson.title} in the context of ${data.title}.`;
        } catch (error) {
          console.error(`Script generation failed for ${lesson.title}:`, error);
          script = `Professional lesson content for ${lesson.title}. This lesson covers the fundamentals and key concepts needed to understand ${lesson.title} in the context of ${data.title}.`;
        }
        
        scripts.push({
          moduleTitle: module.title,
          lessonTitle: lesson.title,
          script
        });
      }
    }
    
    progress.results.scripts = scripts;
    
    // Step 3: Convert content to speech using selected TTS provider
    updateProgress(workflowId, 'Converting scripts to speech', 3);
    
    const audioFiles = [];
    for (const scriptData of scripts) {
      try {
        // Simulate audio generation for now
        const audioPath = `uploads/audio/${workflowId}_${scriptData.lessonTitle.replace(/\s+/g, '_')}.wav`;
        
        // Create placeholder audio file info (in production this would be real TTS)
        audioFiles.push({
          lessonTitle: scriptData.lessonTitle,
          audioPath,
          duration: Math.round(scriptData.script.length / 150 * 60), // Rough estimate
          provider: data.voiceProvider,
          voiceId: data.voiceId || 'default'
        });
        
        console.log(`Audio generated for: ${scriptData.lessonTitle}`);
      } catch (audioError) {
        console.error(`Audio generation failed for ${scriptData.lessonTitle}:`, audioError);
        // Continue with next lesson
      }
    }
    
    progress.results.audioFiles = audioFiles;
    
    // Step 4: Generate presentation slides using Marp
    updateProgress(workflowId, 'Generating presentation slides', 4);
    
    const slideFiles = [];
    for (const scriptData of scripts) {
      try {
        // Create Marp markdown content
        const paragraphs = scriptData.script.split('\n\n');
        const slideContent = paragraphs.map((paragraph: string) => `## ${paragraph.substring(0, 50)}...\n\n${paragraph}`).join('\n\n---\n\n');
        
        const markdownContent = `---
theme: default
paginate: true
---

# ${scriptData.lessonTitle}

---

${slideContent}
`;
        
        const slidePath = `uploads/slides/${workflowId}_${scriptData.lessonTitle.replace(/\s+/g, '_')}.md`;
        fs.writeFileSync(slidePath, markdownContent);
        
        slideFiles.push({
          lessonTitle: scriptData.lessonTitle,
          slidePath
        });
      } catch (slideError) {
        console.error(`Slide generation failed for ${scriptData.lessonTitle}:`, slideError);
      }
    }
    
    progress.results.slideFiles = slideFiles;
    
    // Step 5: Select/search for media files
    updateProgress(workflowId, 'Selecting background media', 5);
    
    // For now, we'll use placeholder media selection
    // In production, this would integrate with Pexels/Pixabay APIs
    progress.results.mediaFiles = audioFiles.map(audio => ({
      lessonTitle: audio.lessonTitle,
      backgroundVideo: 'placeholder_background.mp4'
    }));
    
    // Step 6: Combine audio, media, and slides using FFmpeg
    updateProgress(workflowId, 'Assembling final videos', 6);
    
    const finalVideos = [];
    for (let i = 0; i < audioFiles.length; i++) {
      const audio = audioFiles[i];
      const slide = slideFiles[i];
      
      if (audio && slide) {
        const outputPath = `uploads/videos/${workflowId}_${audio.lessonTitle.replace(/\s+/g, '_')}.mp4`;
        
        // Create basic video from slides and audio
        // This is a simplified version - in production would use proper FFmpeg integration
        finalVideos.push({
          lessonTitle: audio.lessonTitle,
          videoPath: outputPath,
          duration: audio.duration
        });
      }
    }
    
    progress.results.finalVideos = finalVideos;
    
    // Step 7: Generate subtitles (placeholder for Whisper integration)
    updateProgress(workflowId, 'Generating subtitles and captions', 7);
    
    progress.results.subtitles = finalVideos.map(video => ({
      lessonTitle: video.lessonTitle,
      subtitlePath: video.videoPath.replace('.mp4', '.srt')
    }));
    
    // Mark as completed
    progress.status = 'completed';
    progress.currentStep = 'Course creation completed successfully';
    
  } catch (error) {
    console.error('Traditional workflow error:', error);
    progress.status = 'failed';
    progress.error = error instanceof Error ? error.message : 'Unknown error';
  }
}

/**
 * AVATAR COURSE WORKFLOW IMPLEMENTATION
 */
async function processAvatarWorkflow(workflowId: string, data: z.infer<typeof avatarWorkflowSchema>) {
  const progress = workflowProgress.get(workflowId)!;
  
  try {
    // Step 1: Generate course content using Mistral LLM
    updateProgress(workflowId, 'Generating avatar-optimized course content', 1);
    
    const courseStructure = await mistralModalService.generateCourseStructure({
      title: data.title,
      category: data.category,
      description: `Avatar-based ${data.title} course`,
      targetAudience: `${data.difficulty} level learners`,
      useAI: true
    });
    
    progress.results.courseStructure = courseStructure;
    
    // Step 2: Process avatar image
    updateProgress(workflowId, 'Processing avatar image', 2);
    
    if (data.avatarImagePath) {
      progress.results.avatarImage = {
        path: data.avatarImagePath,
        processed: true
      };
    } else {
      throw new Error('Avatar image is required for avatar course workflow');
    }
    
    // Step 3: Generate avatar-optimized scripts
    updateProgress(workflowId, 'Generating avatar presentation scripts', 3);
    
    const avatarScripts = [];
    for (const module of courseStructure.modules || []) {
      for (const lesson of module.lessons || []) {
        // Generate avatar-optimized script using Mistral AI
        let script = '';
        try {
          const scriptResult = await mistralModalService.generateAvatarScript({
            title: lesson.title,
            description: lesson.description || `Comprehensive content for ${lesson.title}`
          });
          script = scriptResult || `Avatar-optimized content for ${lesson.title}. This lesson is designed for engaging avatar presentation covering key concepts of ${lesson.title} in ${data.title}.`;
        } catch (error) {
          console.error(`Avatar script generation failed for ${lesson.title}:`, error);
          script = `Avatar-optimized content for ${lesson.title}. This lesson is designed for engaging avatar presentation covering key concepts of ${lesson.title} in ${data.title}.`;
        }
        
        avatarScripts.push({
          moduleTitle: module.title,
          lessonTitle: lesson.title,
          script
        });
      }
    }
    
    progress.results.avatarScripts = avatarScripts;
    
    // Step 4: Convert scripts to speech
    updateProgress(workflowId, 'Converting scripts to speech', 4);
    
    const audioFiles = [];
    for (const scriptData of avatarScripts) {
      try {
        const audioPath = `uploads/audio/${workflowId}_${scriptData.lessonTitle.replace(/\s+/g, '_')}.wav`;
        const audioResult = await voiceServiceManager.generateSpeech(
          scriptData.script,
          data.voiceId || 'default',
          audioPath,
          {
            speed: 1.0
          }
        );
        
        if (audioResult.success) {
          audioFiles.push({
            lessonTitle: scriptData.lessonTitle,
            audioPath: audioResult.audioPath || audioPath,
            duration: Math.round(scriptData.script.length / 150 * 60)
          });
        } else {
          console.error(`Audio generation failed for ${scriptData.lessonTitle}:`, audioResult.error);
        }
      } catch (audioError) {
        console.error(`Audio generation failed for ${scriptData.lessonTitle}:`, audioError);
      }
    }
    
    progress.results.audioFiles = audioFiles;
    
    // Step 5: Generate talking avatar videos using EchoMimic V2
    updateProgress(workflowId, 'Generating talking avatar videos', 5);
    
    const avatarVideos = [];
    for (const audio of audioFiles) {
      try {
        // This would integrate with EchoMimic V2 API
        // For now, we'll create placeholder results
        const avatarVideoPath = `uploads/avatars/${workflowId}_${audio.lessonTitle.replace(/\s+/g, '_')}_avatar.mp4`;
        
        avatarVideos.push({
          lessonTitle: audio.lessonTitle,
          avatarVideoPath,
          duration: audio.duration
        });
      } catch (avatarError) {
        console.error(`Avatar video generation failed for ${audio.lessonTitle}:`, avatarError);
      }
    }
    
    progress.results.avatarVideos = avatarVideos;
    
    // Step 6: Generate background slides using Marp
    updateProgress(workflowId, 'Generating background presentation slides', 6);
    
    const backgroundSlides = [];
    for (const scriptData of avatarScripts) {
      const slideContent = `---
theme: gaia
class: lead
paginate: false
backgroundColor: #f0f0f0
---

# ${scriptData.lessonTitle}

## Key Points

${scriptData.script.split('\n\n').slice(0, 3).map((point, i) => `${i + 1}. ${point.substring(0, 100)}...`).join('\n')}
`;
        
        const slidePath = `uploads/slides/${workflowId}_${scriptData.lessonTitle.replace(/\s+/g, '_')}_bg.md`;
        fs.writeFileSync(slidePath, slideContent);
        
        backgroundSlides.push({
          lessonTitle: scriptData.lessonTitle,
          backgroundSlidePath: slidePath
        });
    }
    
    progress.results.backgroundSlides = backgroundSlides;
    
    // Step 7: Composite avatar video with background slides
    updateProgress(workflowId, 'Compositing final videos', 7);
    
    const finalVideos = [];
    for (let i = 0; i < avatarVideos.length; i++) {
      const avatar = avatarVideos[i];
      const background = backgroundSlides[i];
      
      if (avatar && background) {
        const compositePath = `uploads/videos/${workflowId}_${avatar.lessonTitle.replace(/\s+/g, '_')}_final.mp4`;
        
        finalVideos.push({
          lessonTitle: avatar.lessonTitle,
          finalVideoPath: compositePath,
          duration: avatar.duration
        });
      }
    }
    
    progress.results.finalVideos = finalVideos;
    
    // Step 8: Add subtitles and captions
    updateProgress(workflowId, 'Adding subtitles and captions', 8);
    
    progress.results.subtitles = finalVideos.map(video => ({
      lessonTitle: video.lessonTitle,
      subtitlePath: video.finalVideoPath.replace('.mp4', '.srt')
    }));
    
    // Mark as completed
    progress.status = 'completed';
    progress.currentStep = 'Avatar course creation completed successfully';
    
  } catch (error) {
    console.error('Avatar workflow error:', error);
    progress.status = 'failed';
    progress.error = error instanceof Error ? error.message : 'Unknown error';
  }
}

/**
 * Update workflow progress
 */
function updateProgress(workflowId: string, step: string, completedSteps: number) {
  const progress = workflowProgress.get(workflowId);
  if (progress) {
    progress.currentStep = step;
    progress.completedSteps = completedSteps;
    console.log(`Workflow ${workflowId}: ${step} (${completedSteps}/${progress.totalSteps})`);
  }
}

/**
 * Clean up old workflow data (run periodically)
 */
setInterval(() => {
  const cutoffTime = Date.now() - (24 * 60 * 60 * 1000); // 24 hours
  
  for (const [workflowId, progress] of workflowProgress.entries()) {
    if (progress.startTime.getTime() < cutoffTime) {
      workflowProgress.delete(workflowId);
    }
  }
}, 60 * 60 * 1000); // Run every hour

export default router;