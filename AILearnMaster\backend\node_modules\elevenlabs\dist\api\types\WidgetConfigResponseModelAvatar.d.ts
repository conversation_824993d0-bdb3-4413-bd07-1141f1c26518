/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as ElevenLabs from "../index";
/**
 * The avatar of the widget
 */
export type WidgetConfigResponseModelAvatar = ElevenLabs.WidgetConfigResponseModelAvatar.Orb | ElevenLabs.WidgetConfigResponseModelAvatar.Url | ElevenLabs.WidgetConfigResponseModelAvatar.Image;
export declare namespace WidgetConfigResponseModelAvatar {
    interface Orb extends ElevenLabs.OrbAvatar {
        type: "orb";
    }
    interface Url extends ElevenLabs.UrlAvatar {
        type: "url";
    }
    interface Image extends ElevenLabs.ImageAvatar {
        type: "image";
    }
}
