/**
 * This file was auto-generated by Fern from our API Definition.
 */
/**
 * The time interval over which to aggregate the usage data.
 */
export type UsageAggregationInterval = "hour" | "day" | "week" | "month" | "cumulative";
export declare const UsageAggregationInterval: {
    readonly Hour: "hour";
    readonly Day: "day";
    readonly Week: "week";
    readonly Month: "month";
    readonly Cumulative: "cumulative";
};
