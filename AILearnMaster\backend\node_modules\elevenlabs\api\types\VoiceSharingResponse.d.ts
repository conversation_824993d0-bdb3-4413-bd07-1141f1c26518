/**
 * This file was auto-generated by <PERSON><PERSON> from our API Definition.
 */
import * as ElevenLabs from "../index";
export interface VoiceSharingResponse {
    /** The status of the voice sharing. */
    status?: ElevenLabs.VoiceSharingState;
    /** The sample ID of the history item. */
    history_item_sample_id?: string;
    /** The date of the voice sharing in Unix time. */
    date_unix?: number;
    /** A list of whitelisted emails. */
    whitelisted_emails?: string[];
    /** The ID of the public owner. */
    public_owner_id?: string;
    /** The ID of the original voice. */
    original_voice_id?: string;
    /** Whether financial rewards are enabled. */
    financial_rewards_enabled?: boolean;
    /** Whether free users are allowed. */
    free_users_allowed?: boolean;
    /** Whether live moderation is enabled. */
    live_moderation_enabled?: boolean;
    /** The rate of the voice sharing. */
    rate?: number;
    /** The notice period of the voice sharing. */
    notice_period?: number;
    /** The date of the voice sharing in Unix time. */
    disable_at_unix?: number;
    /** Whether voice mixing is allowed. */
    voice_mixing_allowed?: boolean;
    /** Whether the voice is featured. */
    featured?: boolean;
    /** The category of the voice. */
    category?: ElevenLabs.VoiceSharingResponseModelCategory;
    /** Whether the reader app is enabled. */
    reader_app_enabled?: boolean;
    /** The image URL of the voice. */
    image_url?: string;
    /** The ban reason of the voice. */
    ban_reason?: string;
    /** The number of likes on the voice. */
    liked_by_count?: number;
    /** The number of clones on the voice. */
    cloned_by_count?: number;
    /** The name of the voice. */
    name?: string;
    /** The description of the voice. */
    description?: string;
    /** The labels of the voice. */
    labels?: Record<string, string>;
    /** The review status of the voice. */
    review_status?: ElevenLabs.ReviewStatus;
    /** The review message of the voice. */
    review_message?: string;
    /** Whether the voice is enabled in the library. */
    enabled_in_library?: boolean;
    /** The Instagram username of the voice. */
    instagram_username?: string;
    /** The Twitter/X username of the voice. */
    twitter_username?: string;
    /** The YouTube username of the voice. */
    youtube_username?: string;
    /** The TikTok username of the voice. */
    tiktok_username?: string;
    /** The moderation check of the voice. */
    moderation_check?: ElevenLabs.VoiceSharingModerationCheckResponseModel;
    /** The reader restricted on of the voice. */
    reader_restricted_on?: ElevenLabs.ReaderResourceResponseModel[];
}
