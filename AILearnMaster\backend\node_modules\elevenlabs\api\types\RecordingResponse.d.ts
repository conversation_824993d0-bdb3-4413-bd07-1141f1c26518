/**
 * This file was auto-generated by Fern from our API Definition.
 */
export interface RecordingResponse {
    /** The ID of the recording. */
    recording_id: string;
    /** The MIME type of the recording. */
    mime_type: string;
    /** The size of the recording in bytes. */
    size_bytes: number;
    /** The date of the recording in Unix time. */
    upload_date_unix: number;
    /** The transcription of the recording. */
    transcription: string;
}
