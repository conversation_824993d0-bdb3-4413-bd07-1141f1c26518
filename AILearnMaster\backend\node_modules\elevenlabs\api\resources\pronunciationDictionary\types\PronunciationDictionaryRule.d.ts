/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as ElevenLabs from "../../../index";
export type PronunciationDictionaryRule = ElevenLabs.PronunciationDictionaryRule.Alias | ElevenLabs.PronunciationDictionaryRule.Phoneme;
export declare namespace PronunciationDictionaryRule {
    interface Alias extends ElevenLabs.PronunciationDictionaryAliasRuleRequestModel {
        type: "alias";
    }
    interface Phoneme extends ElevenLabs.PronunciationDictionaryPhonemeRuleRequestModel {
        type: "phoneme";
    }
}
