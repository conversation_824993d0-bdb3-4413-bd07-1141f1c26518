import { db } from '../db';
import { templates } from '../shared-schema-alias';

export const defaultTemplates = [
  {
    name: "Course Structure Generator",
    description: "Generate a comprehensive course structure with modules and lessons using AI",
    icon: "BookOpen",
    type: "course-generation",
    category: "Education",
    structure: {
      apiEndpoint: "/api/ai/generate-course-structure",
      method: "POST",
      fields: [
        {
          name: "title",
          label: "Course Title",
          type: "text",
          placeholder: "Enter your course title",
          required: true
        },
        {
          name: "description",
          label: "Course Description", 
          type: "textarea",
          placeholder: "Describe what your course will teach",
          required: true
        },
        {
          name: "category",
          label: "Category",
          type: "select",
          options: [
            "Technology",
            "Business", 
            "Marketing",
            "Design",
            "Health & Wellness",
            "Personal Development",
            "Language Learning",
            "Academic",
            "Creative Arts",
            "Science"
          ],
          required: true
        },
        {
          name: "moduleCount",
          label: "Number of Modules",
          type: "number",
          min: 3,
          max: 12,
          default: 6
        }
      ],
      outputFormat: "course-structure",
      creditsRequired: 5
    }
  },
  {
    name: "Voice Generation",
    description: "Convert text to natural-sounding speech using advanced TTS technology",
    icon: "Mic",
    type: "voice-generation",
    category: "Audio",
    structure: {
      apiEndpoint: "/api/voice-generation/generate",
      method: "POST",
      fields: [
        {
          name: "text",
          label: "Text to Convert",
          type: "textarea",
          placeholder: "Enter the text you want to convert to speech",
          required: true,
          maxLength: 2000
        },
        {
          name: "voiceService",
          label: "Voice Service",
          type: "select",
          options: [
            { value: "chatterbox", label: "Chatterbox TTS (Enterprise)" },
            { value: "openai", label: "OpenAI TTS (Premium)" },
            { value: "elevenlabs", label: "ElevenLabs (Premium)" }
          ],
          default: "chatterbox"
        },
        {
          name: "speed",
          label: "Speech Speed",
          type: "range",
          min: 0.5,
          max: 2.0,
          step: 0.1,
          default: 1.0
        }
      ],
      outputFormat: "audio-file",
      creditsRequired: 2
    }
  },
  {
    name: "AI Image Generator",
    description: "Create stunning images using SDXL on A100 GPU for course thumbnails and content",
    icon: "Image",
    type: "image-generation", 
    category: "Visual",
    structure: {
      apiEndpoint: "/api/modal/generate-image",
      method: "POST",
      fields: [
        {
          name: "prompt",
          label: "Image Description",
          type: "textarea",
          placeholder: "Describe the image you want to generate",
          required: true
        },
        {
          name: "style",
          label: "Image Style",
          type: "select",
          options: [
            "Professional",
            "Educational",
            "Creative",
            "Minimalist",
            "Artistic",
            "Corporate",
            "Modern"
          ],
          default: "Professional"
        },
        {
          name: "dimensions",
          label: "Image Size",
          type: "select",
          options: [
            { value: "1024x1024", label: "Square (1024x1024)" },
            { value: "1024x768", label: "Landscape (1024x768)" },
            { value: "768x1024", label: "Portrait (768x1024)" }
          ],
          default: "1024x1024"
        }
      ],
      outputFormat: "image-url",
      creditsRequired: 4
    }
  },
  {
    name: "Quiz Generator",
    description: "Generate interactive quizzes and assessments for your courses",
    icon: "HelpCircle",
    type: "assessment-generation",
    category: "Education",
    structure: {
      apiEndpoint: "/api/ai/generate-quiz-questions",
      method: "POST",
      fields: [
        {
          name: "content",
          label: "Course Content",
          type: "textarea",
          placeholder: "Paste the content to base quiz questions on",
          required: true
        },
        {
          name: "questionCount",
          label: "Number of Questions",
          type: "number",
          min: 3,
          max: 20,
          default: 5
        },
        {
          name: "difficulty",
          label: "Difficulty Level",
          type: "select",
          options: ["easy", "medium", "hard"],
          default: "medium"
        },
        {
          name: "questionTypes",
          label: "Question Types",
          type: "multi-select",
          options: [
            { value: "multiple_choice", label: "Multiple Choice" },
            { value: "true_false", label: "True/False" },
            { value: "short_answer", label: "Short Answer" }
          ],
          default: ["multiple_choice"]
        }
      ],
      outputFormat: "quiz-data",
      creditsRequired: 3
    }
  },
  {
    name: "Avatar Video Creator",
    description: "Generate talking head videos using SadTalker technology with your content",
    icon: "Video",
    type: "video-generation",
    category: "Video",
    structure: {
      apiEndpoint: "/api/sadtalker/generate-video",
      method: "POST",
      fields: [
        {
          name: "script",
          label: "Video Script",
          type: "textarea",
          placeholder: "Enter the script for your avatar video",
          required: true,
          maxLength: 1000
        },
        {
          name: "avatarImage",
          label: "Avatar Image",
          type: "file-upload",
          accept: "image/*",
          required: true
        },
        {
          name: "voiceService",
          label: "Voice Service",
          type: "select",
          options: [
            { value: "chatterbox", label: "Chatterbox TTS" },
            { value: "openai", label: "OpenAI TTS" }
          ],
          default: "chatterbox"
        }
      ],
      outputFormat: "video-file",
      creditsRequired: 6
    }
  },
  {
    name: "Slide Presentation Generator",
    description: "Create professional presentation slides using Marp from your content",
    icon: "Presentation",
    type: "presentation-generation",
    category: "Visual",
    structure: {
      apiEndpoint: "/api/marp/generate-slides",
      method: "POST",
      fields: [
        {
          name: "content",
          label: "Presentation Content",
          type: "textarea",
          placeholder: "Enter your presentation content in markdown format",
          required: true
        },
        {
          name: "theme",
          label: "Presentation Theme",
          type: "select",
          options: [
            "Professional",
            "Academic", 
            "Creative",
            "Corporate",
            "Minimal"
          ],
          default: "Professional"
        },
        {
          name: "slideCount",
          label: "Target Slide Count",
          type: "number",
          min: 5,
          max: 50,
          default: 10
        }
      ],
      outputFormat: "slide-presentation",
      creditsRequired: 3
    }
  },
  {
    name: "Lesson Script Creator",
    description: "Create detailed lesson scripts with engaging content and learning objectives",
    icon: "FileText",
    type: "content-generation",
    category: "Education",
    structure: {
      apiEndpoint: "/api/ai/generate-lesson-content",
      method: "POST",
      fields: [
        {
          name: "lessonTitle",
          label: "Lesson Title",
          type: "text",
          placeholder: "Enter lesson title",
          required: true
        },
        {
          name: "learningObjectives",
          label: "Learning Objectives",
          type: "textarea",
          placeholder: "What should students learn from this lesson?",
          required: true
        },
        {
          name: "duration",
          label: "Target Duration (minutes)",
          type: "number",
          min: 5,
          max: 120,
          default: 15
        },
        {
          name: "difficulty",
          label: "Difficulty Level",
          type: "select",
          options: ["Beginner", "Intermediate", "Advanced"],
          default: "Intermediate"
        }
      ],
      outputFormat: "lesson-script",
      creditsRequired: 3
    }
  },
  {
    name: "Traditional Course Generator",
    description: "Generate complete traditional courses with Enhanced Video Production Studio",
    icon: "GraduationCap",
    type: "full-course-generation",
    category: "Education",
    structure: {
      apiEndpoint: "/api/enhanced-course/generate-traditional",
      method: "POST",
      fields: [
        {
          name: "title",
          label: "Course Title",
          type: "text",
          placeholder: "Enter course title",
          required: true
        },
        {
          name: "description",
          label: "Course Description",
          type: "textarea",
          placeholder: "Describe your course content and objectives",
          required: true
        },
        {
          name: "category",
          label: "Category",
          type: "select",
          options: [
            "Technology",
            "Business",
            "Marketing", 
            "Design",
            "Health & Wellness",
            "Personal Development"
          ],
          required: true
        },
        {
          name: "targetAudience",
          label: "Target Audience",
          type: "text",
          placeholder: "Who is this course for?",
          required: true
        }
      ],
      outputFormat: "complete-course",
      creditsRequired: 10
    }
  },
  {
    name: "Content Summarizer",
    description: "Create concise summaries of long-form content using advanced AI",
    icon: "FileText",
    type: "content-summarization",
    category: "Productivity",
    structure: {
      apiEndpoint: "/api/ai/summarize-content",
      method: "POST",
      fields: [
        {
          name: "content",
          label: "Content to Summarize",
          type: "textarea",
          placeholder: "Paste your content here for summarization",
          required: true,
          maxLength: 10000
        },
        {
          name: "summaryLength",
          label: "Summary Length",
          type: "select",
          options: ["Brief", "Medium", "Detailed"],
          default: "Medium"
        },
        {
          name: "format",
          label: "Output Format",
          type: "select",
          options: ["Paragraph", "Bullet Points", "Key Takeaways"],
          default: "Paragraph"
        }
      ],
      outputFormat: "summary-text",
      creditsRequired: 2
    }
  },
  {
    name: "Learning Path Creator",
    description: "Generate personalized learning paths with milestones and resources",
    icon: "Map",
    type: "learning-path-generation",
    category: "Education",
    structure: {
      apiEndpoint: "/api/ai/generate-learning-path",
      method: "POST",
      fields: [
        {
          name: "subject",
          label: "Learning Subject",
          type: "text",
          placeholder: "What do you want to learn?",
          required: true
        },
        {
          name: "currentLevel",
          label: "Current Knowledge Level",
          type: "select",
          options: ["Beginner", "Intermediate", "Advanced"],
          default: "Beginner"
        },
        {
          name: "timeCommitment",
          label: "Weekly Time Commitment",
          type: "select",
          options: ["1-2 hours", "3-5 hours", "6-10 hours", "10+ hours"],
          default: "3-5 hours"
        },
        {
          name: "learningStyle",
          label: "Preferred Learning Style",
          type: "multi-select",
          options: ["Visual", "Auditory", "Reading", "Hands-on"],
          default: ["Visual", "Hands-on"]
        }
      ],
      outputFormat: "learning-path",
      creditsRequired: 4
    }
  },
  {
    name: "Marketing Copy Generator",
    description: "Create compelling marketing copy for various platforms and purposes",
    icon: "Megaphone",
    type: "marketing-generation",
    category: "Marketing",
    structure: {
      apiEndpoint: "/api/ai/generate-marketing-copy",
      method: "POST",
      fields: [
        {
          name: "product",
          label: "Product/Service Name",
          type: "text",
          placeholder: "What are you marketing?",
          required: true
        },
        {
          name: "description",
          label: "Product Description",
          type: "textarea",
          placeholder: "Describe your product/service",
          required: true
        },
        {
          name: "platform",
          label: "Marketing Platform",
          type: "select",
          options: ["Social Media", "Email", "Website", "Advertisement", "Blog Post"],
          required: true
        },
        {
          name: "tone",
          label: "Tone of Voice",
          type: "select",
          options: ["Professional", "Casual", "Friendly", "Urgent", "Inspirational"],
          default: "Professional"
        },
        {
          name: "targetAudience",
          label: "Target Audience",
          type: "text",
          placeholder: "Who is your target audience?",
          required: true
        }
      ],
      outputFormat: "marketing-copy",
      creditsRequired: 3
    }
  },
  {
    name: "Code Documentation Generator",
    description: "Generate comprehensive documentation for code projects and functions",
    icon: "Code",
    type: "documentation-generation",
    category: "Development",
    structure: {
      apiEndpoint: "/api/ai/generate-documentation",
      method: "POST",
      fields: [
        {
          name: "code",
          label: "Code to Document",
          type: "textarea",
          placeholder: "Paste your code here",
          required: true
        },
        {
          name: "language",
          label: "Programming Language",
          type: "select",
          options: ["JavaScript", "Python", "Java", "C++", "TypeScript", "React", "Other"],
          required: true
        },
        {
          name: "documentationType",
          label: "Documentation Type",
          type: "select",
          options: ["API Reference", "User Guide", "Code Comments", "README"],
          default: "API Reference"
        },
        {
          name: "includeExamples",
          label: "Include Usage Examples",
          type: "boolean",
          default: true
        }
      ],
      outputFormat: "documentation",
      creditsRequired: 3
    }
  },
  {
    name: "Email Template Creator",
    description: "Generate professional email templates for various business purposes",
    icon: "Mail",
    type: "email-generation",
    category: "Business",
    structure: {
      apiEndpoint: "/api/ai/generate-email-template",
      method: "POST",
      fields: [
        {
          name: "purpose",
          label: "Email Purpose",
          type: "select",
          options: [
            "Welcome Email",
            "Follow-up",
            "Meeting Request",
            "Proposal",
            "Thank You",
            "Apology",
            "Newsletter",
            "Sales Outreach"
          ],
          required: true
        },
        {
          name: "recipientType",
          label: "Recipient Type",
          type: "select",
          options: ["Client", "Colleague", "Customer", "Vendor", "Team Member"],
          required: true
        },
        {
          name: "tone",
          label: "Email Tone",
          type: "select",
          options: ["Formal", "Semi-formal", "Friendly", "Professional"],
          default: "Professional"
        },
        {
          name: "context",
          label: "Additional Context",
          type: "textarea",
          placeholder: "Provide any specific details or context",
          required: false
        }
      ],
      outputFormat: "email-template",
      creditsRequired: 2
    }
  },
  {
    name: "Social Media Content Planner",
    description: "Create content calendars and posts for social media platforms",
    icon: "Calendar",
    type: "social-media-generation",
    category: "Marketing",
    structure: {
      apiEndpoint: "/api/ai/generate-social-content",
      method: "POST",
      fields: [
        {
          name: "brand",
          label: "Brand/Company Name",
          type: "text",
          placeholder: "Your brand name",
          required: true
        },
        {
          name: "platforms",
          label: "Social Media Platforms",
          type: "multi-select",
          options: ["Instagram", "Twitter", "LinkedIn", "Facebook", "TikTok", "YouTube"],
          required: true
        },
        {
          name: "contentType",
          label: "Content Focus",
          type: "select",
          options: ["Educational", "Promotional", "Behind-the-scenes", "User-generated", "News/Updates"],
          required: true
        },
        {
          name: "postCount",
          label: "Number of Posts",
          type: "number",
          min: 1,
          max: 30,
          default: 7
        },
        {
          name: "industry",
          label: "Industry/Niche",
          type: "text",
          placeholder: "Your industry or niche",
          required: true
        }
      ],
      outputFormat: "social-content-plan",
      creditsRequired: 4
    }
  },
  {
    name: "Research Paper Outline",
    description: "Generate structured outlines for academic research papers and essays",
    icon: "BookOpen",
    type: "academic-generation",
    category: "Academic",
    structure: {
      apiEndpoint: "/api/ai/generate-research-outline",
      method: "POST",
      fields: [
        {
          name: "topic",
          label: "Research Topic",
          type: "text",
          placeholder: "Enter your research topic",
          required: true
        },
        {
          name: "paperType",
          label: "Paper Type",
          type: "select",
          options: ["Research Paper", "Essay", "Thesis", "Literature Review", "Case Study"],
          required: true
        },
        {
          name: "academicLevel",
          label: "Academic Level",
          type: "select",
          options: ["High School", "Undergraduate", "Graduate", "PhD"],
          default: "Undergraduate"
        },
        {
          name: "pageLength",
          label: "Target Length",
          type: "select",
          options: ["5-10 pages", "10-15 pages", "15-20 pages", "20+ pages"],
          default: "10-15 pages"
        },
        {
          name: "citationStyle",
          label: "Citation Style",
          type: "select",
          options: ["APA", "MLA", "Chicago", "Harvard"],
          default: "APA"
        }
      ],
      outputFormat: "research-outline",
      creditsRequired: 3
    }
  }
];

export async function seedTemplates() {
  try {
    console.log('🌱 Seeding default templates...');
    
    for (const template of defaultTemplates) {
      if (db) {
        await db.insert(templates).values(template).onConflictDoNothing();
      }
    }
    
    console.log(`✅ Successfully seeded ${defaultTemplates.length} templates`);
  } catch (error) {
    console.error('❌ Error seeding templates:', error);
    throw error;
  }
}