/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as ElevenLabs from "../index";
export interface SubscriptionExtrasResponseModel {
    /** The concurrency of the user. */
    concurrency: number;
    /** The Convai concurrency of the user. */
    convai_concurrency: number;
    /** The Convai characters per minute of the user. */
    convai_chars_per_minute?: number;
    /** The Convai ASR characters per minute of the user. */
    convai_asr_chars_per_minute?: number;
    /** Whether the user's logging is disabled. */
    force_logging_disabled: boolean;
    /** Whether the user can request manual pro voice verification. */
    can_request_manual_pro_voice_verification: boolean;
    /** Whether the user can bypass the voice captcha. */
    can_bypass_voice_captcha: boolean;
    /** The moderation status of the user. */
    moderation: ElevenLabs.ModerationStatusResponseModel;
    /** The unused characters rolled over from the previous period. */
    unused_characters_rolled_over_from_previous_period?: number;
    /** The overused characters rolled over from the previous period. */
    overused_characters_rolled_over_from_previous_period?: number;
    /** Data on how the subscription is being used. */
    usage?: ElevenLabs.SubscriptionUsageResponseModel;
}
