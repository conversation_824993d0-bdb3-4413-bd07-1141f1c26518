/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as ElevenLabs from "../index";
export interface VoiceSample {
    /** The ID of the sample. */
    sample_id?: string;
    /** The name of the sample file. */
    file_name?: string;
    /** The MIME type of the sample file. */
    mime_type?: string;
    /** The size of the sample file in bytes. */
    size_bytes?: number;
    /** The hash of the sample file. */
    hash?: string;
    duration_secs?: number;
    remove_background_noise?: boolean;
    has_isolated_audio?: boolean;
    has_isolated_audio_preview?: boolean;
    speaker_separation?: ElevenLabs.SpeakerSeparationResponseModel;
    trim_start?: number;
    trim_end?: number;
}
