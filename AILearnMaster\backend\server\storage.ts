import { 
  users, type User, type InsertUser,
  courses, type Course, type InsertCourse,
  modules, type Module, type InsertModule,
  lessons, type Lesson, type InsertLesson,
  mediaLibrary, type Media, type InsertMedia,
  integrations, type Integration, type InsertIntegration,
  userStats, type UserStats, type InsertUserStats,
  templates, type Template,
  templateHistory, type TemplateHistory, type InsertTemplateHistory,
  teams, type Team, type InsertTeam,
  teamMembers, type TeamMember, type InsertTeamMember,
  courseCollaborators, type CourseCollaborator, type InsertCourseCollaborator,
  teamCourses, type TeamCourse, type InsertTeamCourse,
  courseAnalytics, type CourseAnalytics, type InsertCourseAnalytics,
  lessonAnalytics, type LessonAnalytics, type InsertLessonAnalytics,
  userCourseProgress, type UserCourseProgress, type InsertUserCourseProgress,
  userLessonProgressRecord as userLessonProgress, type UserLessonProgress, type InsertUserLessonProgress,
  analyticsEvents, type AnalyticsEvent, type InsertAnalyticsEvent,
  publishing, type Publishing, type InsertPublishing,
  ttsRecords, type TTSRecord, type InsertTTSRecord, type TTSResponse,
  aiGeneratedImages, type AiGeneratedImage, type InsertAiGeneratedImage,
  generatedVideos, type GeneratedVideo, type InsertGeneratedVideo,
  miniCourses, type MiniCourse, type InsertMiniCourse,
  userApiKeys, type UserApiKey, type InsertUserApiKey,
  landingPages, type LandingPage, type InsertLandingPage,
  landingPageVisits, type LandingPageVisit, type InsertLandingPageVisit,
  microLearningSegments, type MicroLearningSegment, type InsertMicroLearningSegment,
  microLearningKnowledgeChecks, type MicroLearningKnowledgeCheck, type InsertMicroLearningKnowledgeCheck,
  microLearningUserProgress, type MicroLearningUserProgress, type InsertMicroLearningUserProgress,
  userKnowledgeCheckResponses, type UserKnowledgeCheckResponse, type InsertUserKnowledgeCheckResponse,
  courseDrafts, type CourseDraft, type InsertCourseDraft
} from "@shared/schema";
import session from "express-session";
import { db, pool } from "./db";
import { eq, and, desc, asc, sql, or, inArray } from "drizzle-orm";
import connectPgSimple from "connect-pg-simple";
import { z } from "zod";
import crypto from "crypto";
import { AuthService } from "./services/auth";

export interface IStorage {
  sessionStore: session.Store;
  // User operations
  getUser(id: number): Promise<User | undefined>;
  getUserByUsername(username: string): Promise<User | undefined>;
  getUserByEmail(email: string): Promise<User | undefined>;
  getAllUsers(): Promise<User[]>;
  createUser(user: InsertUser): Promise<User>;
  updateUser(id: number, user: Partial<User>): Promise<User | undefined>;
  updateStripeCustomerId(userId: number, customerId: string): Promise<User | undefined>;
  updateUserStripeInfo(userId: number, stripeInfo: { customerId: string, subscriptionId: string }): Promise<User | undefined>;
  
  // Password reset operations
  setPasswordResetToken(email: string): Promise<string | null>;
  getUserByResetToken(token: string): Promise<User | undefined>;
  resetPassword(token: string, newPassword: string): Promise<boolean>;
  
  // Platform integration operations
  getAllPlatforms(userId?: number): Promise<any[]>;
  getPlatformBySlug(slug: string): Promise<any | undefined>;
  connectPlatform(userId: number, platformSlug: string): Promise<boolean>;
  disconnectPlatform(userId: number, platformId: number): Promise<boolean>;
  isPlatformConnected(userId: number, platformSlug: string): Promise<boolean>;
  publishCourseToPlatform(userId: number, courseId: number, platformSlug: string, options?: any): Promise<any>;
  getCoursePublications(userId: number, courseId: number): Promise<any[]>;
  isUserCourseCollaborator(userId: number, courseId: number): Promise<boolean>;
  
  // Platform integration methods
  createPlatformConnection(data: any): Promise<any>;
  getPlatformConnection(id: number): Promise<any>;
  getPlatformConnectionByUserAndPlatform(userId: number, platformSlug: string): Promise<any>;
  updatePlatformConnectionLastSynced(id: number): Promise<any>;
  deletePlatformConnection(id: number): Promise<boolean>;
  
  // Course operations
  getCourse(id: number): Promise<Course | undefined>;
  getCoursesByUserId(userId: number): Promise<Course[]>;
  createCourse(course: InsertCourse): Promise<Course>;
  updateCourse(id: number, course: Partial<Course>): Promise<Course | undefined>;
  deleteCourse(id: number): Promise<boolean>;
  
  // Module operations
  getModule(id: number): Promise<Module | undefined>;
  getModulesByCourseId(courseId: number): Promise<Module[]>;
  createModule(module: InsertModule): Promise<Module>;
  updateModule(id: number, module: Partial<Module>): Promise<Module | undefined>;
  deleteModule(id: number): Promise<boolean>;
  
  // Lesson operations
  getLesson(id: number): Promise<Lesson | undefined>;
  getLessonsByCourseId(courseId: number): Promise<Lesson[]>;
  getLessonsByModuleId(moduleId: number): Promise<Lesson[]>;
  createLesson(lesson: InsertLesson): Promise<Lesson>;
  updateLesson(id: number, lesson: Partial<Lesson>): Promise<Lesson | undefined>;
  deleteLesson(id: number): Promise<boolean>;
  
  // Media operations
  getMedia(id: number): Promise<Media | undefined>;
  getMediaByUserId(userId: number): Promise<Media[]>;
  getMediaById(id: number): Promise<Media | undefined>;
  getMediaBySourceId(sourceId: string): Promise<Media | undefined>;
  getRandomMedia(count: number, types?: string[]): Promise<Media[]>;
  createMedia(media: InsertMedia): Promise<Media>;
  deleteMedia(id: number): Promise<boolean>;
  updateMedia(id: number, updates: Partial<Media>): Promise<Media | undefined>;
  
  // Integration operations
  getIntegrationsByUserId(userId: number): Promise<Integration[]>;
  createIntegration(integration: InsertIntegration): Promise<Integration>;
  updateIntegration(id: number, integration: Partial<Integration>): Promise<Integration | undefined>;
  
  // Publishing operations
  getPublishingByCourseId(courseId: number): Promise<Publishing[]>;
  getPublishingByPlatform(platform: string): Promise<Publishing[]>;
  createPublishing(insertPublishing: InsertPublishing): Promise<Publishing>;
  updatePublishing(id: number, publishingUpdate: Partial<Publishing>): Promise<Publishing | undefined>;
  deletePublishing(id: number): Promise<boolean>;
  
  // User stats operations
  getUserStats(userId: number): Promise<UserStats | undefined>;
  createUserStats(stats: InsertUserStats): Promise<UserStats>;
  
  // Micro-learning operations
  getMicroLearningSegment(id: number): Promise<MicroLearningSegment | undefined>;
  getMicroLearningSegmentsByLessonId(lessonId: number): Promise<MicroLearningSegment[]>;
  createMicroLearningSegment(segment: InsertMicroLearningSegment): Promise<MicroLearningSegment>;
  updateMicroLearningSegment(id: number, segment: Partial<MicroLearningSegment>): Promise<MicroLearningSegment | undefined>;
  deleteMicroLearningSegment(id: number): Promise<boolean>;
  
  getMicroLearningKnowledgeCheck(id: number): Promise<MicroLearningKnowledgeCheck | undefined>;
  getMicroLearningKnowledgeChecksByLessonId(lessonId: number): Promise<MicroLearningKnowledgeCheck[]>;
  createMicroLearningKnowledgeCheck(check: InsertMicroLearningKnowledgeCheck): Promise<MicroLearningKnowledgeCheck>;
  updateMicroLearningKnowledgeCheck(id: number, check: Partial<MicroLearningKnowledgeCheck>): Promise<MicroLearningKnowledgeCheck | undefined>;
  deleteMicroLearningKnowledgeCheck(id: number): Promise<boolean>;
  
  getMicroLearningUserProgress(id: number): Promise<MicroLearningUserProgress | undefined>;
  getMicroLearningUserProgressByUserAndLesson(userId: number, lessonId: number): Promise<MicroLearningUserProgress | undefined>;
  createMicroLearningUserProgress(progress: InsertMicroLearningUserProgress): Promise<MicroLearningUserProgress>;
  updateMicroLearningUserProgress(id: number, progress: Partial<MicroLearningUserProgress>): Promise<MicroLearningUserProgress | undefined>;
  
  getUserKnowledgeCheckResponse(id: number): Promise<UserKnowledgeCheckResponse | undefined>;
  getUserKnowledgeCheckResponsesByLessonId(userId: number, lessonId: number): Promise<UserKnowledgeCheckResponse[]>;
  createUserKnowledgeCheckResponse(response: InsertUserKnowledgeCheckResponse): Promise<UserKnowledgeCheckResponse>;
  updateUserStats(userId: number, stats: Partial<UserStats>): Promise<UserStats | undefined>;
  
  // Template operations
  getAllTemplates(): Promise<Template[]>;
  getTemplate(id: number): Promise<Template | undefined>;
  
  // Template History operations
  getTemplateHistory(id: number): Promise<TemplateHistory | undefined>;
  getTemplateHistoryByUserId(userId: number): Promise<TemplateHistory[]>;
  createTemplateHistory(history: InsertTemplateHistory): Promise<TemplateHistory>;
  updateTemplateHistoryFavorite(id: number, favorited: boolean): Promise<TemplateHistory | undefined>;
  
  // Team operations
  getTeam(id: number): Promise<Team | undefined>;
  getTeamsByUserId(userId: number): Promise<Team[]>;
  createTeam(team: InsertTeam): Promise<Team>;
  updateTeam(id: number, team: Partial<Team>): Promise<Team | undefined>;
  deleteTeam(id: number): Promise<boolean>;
  
  // Team Members operations
  getTeamMembers(teamId: number): Promise<TeamMember[]>;
  getTeamMembersByUserId(userId: number): Promise<TeamMember[]>;
  addTeamMember(teamMember: InsertTeamMember): Promise<TeamMember>;
  updateTeamMemberRole(teamId: number, userId: number, role: string): Promise<TeamMember | undefined>;
  removeTeamMember(teamId: number, userId: number): Promise<boolean>;
  
  // Course Collaborators operations
  getCourseCollaborators(courseId: number): Promise<CourseCollaborator[]>;
  getUserCollaboratedCourses(userId: number): Promise<Course[]>;
  addCourseCollaborator(collaborator: InsertCourseCollaborator): Promise<CourseCollaborator>;
  updateCourseCollaborator(courseId: number, userId: number, update: Partial<CourseCollaborator>): Promise<CourseCollaborator | undefined>;
  removeCourseCollaborator(courseId: number, userId: number): Promise<boolean>;
  
  // Team Courses operations
  getTeamCourses(teamId: number): Promise<Course[]>;
  addCourseToTeam(teamCourse: InsertTeamCourse): Promise<TeamCourse>;
  removeCourseFromTeam(teamId: number, courseId: number): Promise<boolean>;
  
  // Course Analytics operations
  getCourseAnalytics(courseId: number): Promise<CourseAnalytics | undefined>;
  createCourseAnalytics(analytics: InsertCourseAnalytics): Promise<CourseAnalytics>;
  updateCourseAnalytics(courseId: number, analytics: Partial<CourseAnalytics>): Promise<CourseAnalytics | undefined>;
  
  // Lesson Analytics operations
  getLessonAnalytics(lessonId: number): Promise<LessonAnalytics | undefined>;
  getLessonAnalyticsByCourseId(courseId: number): Promise<LessonAnalytics[]>;
  createLessonAnalytics(analytics: InsertLessonAnalytics): Promise<LessonAnalytics>;
  updateLessonAnalytics(lessonId: number, analytics: Partial<LessonAnalytics>): Promise<LessonAnalytics | undefined>;
  
  // User Course Progress operations
  getUserCourseProgress(userId: number, courseId: number): Promise<UserCourseProgress | undefined>;
  getUserCourseProgressByUserId(userId: number): Promise<UserCourseProgress[]>;
  getUsersByCourseId(courseId: number): Promise<UserCourseProgress[]>;
  createUserCourseProgress(progress: InsertUserCourseProgress): Promise<UserCourseProgress>;
  updateUserCourseProgress(userId: number, courseId: number, progress: Partial<UserCourseProgress>): Promise<UserCourseProgress | undefined>;
  
  // User Lesson Progress operations
  getUserLessonProgress(userId: number, lessonId: number): Promise<UserLessonProgress | undefined>;
  getUserLessonProgressByCourseId(userId: number, courseId: number): Promise<UserLessonProgress[]>;
  createUserLessonProgress(progress: InsertUserLessonProgress): Promise<UserLessonProgress>;
  updateUserLessonProgress(userId: number, lessonId: number, progress: Partial<UserLessonProgress>): Promise<UserLessonProgress | undefined>;
  
  // Analytics Events operations
  recordAnalyticsEvent(event: InsertAnalyticsEvent): Promise<AnalyticsEvent>;
  getAnalyticsEventsByCourseId(courseId: number, limit?: number): Promise<AnalyticsEvent[]>;
  getAnalyticsEventsByUserId(userId: number, limit?: number): Promise<AnalyticsEvent[]>;
  getAnalyticsEventsByLessonId(lessonId: number, limit?: number): Promise<AnalyticsEvent[]>;
  
  // Dashboard Analytics
  getCoursePerformanceOverview(courseId: number): Promise<any>;
  getUserEngagementStats(userId: number): Promise<any>;
  getPopularCourses(limit?: number): Promise<any[]>;
  
  // AI Generated Images operations
  getAiGeneratedImage(id: number): Promise<AiGeneratedImage | undefined>;
  getAiGeneratedImagesByUserId(userId: number): Promise<AiGeneratedImage[]>;
  createAiGeneratedImage(image: InsertAiGeneratedImage): Promise<AiGeneratedImage>;
  deleteAiGeneratedImage(id: number): Promise<boolean>;

  // Generated Videos operations
  getGeneratedVideo(id: string): Promise<GeneratedVideo | undefined>;
  getGeneratedVideosByUserId(userId: number): Promise<GeneratedVideo[]>;
  createGeneratedVideo(video: InsertGeneratedVideo): Promise<GeneratedVideo>;
  updateGeneratedVideo(id: string, video: Partial<GeneratedVideo>): Promise<GeneratedVideo | undefined>;
  deleteGeneratedVideo(id: string): Promise<boolean>;
  
  // Mini Course operations
  getMiniCourse(id: number): Promise<MiniCourse | undefined>;
  getMiniCoursesByUserId(userId: number): Promise<MiniCourse[]>;
  createMiniCourse(miniCourse: InsertMiniCourse): Promise<MiniCourse>;
  updateMiniCourse(id: number, miniCourse: Partial<MiniCourse>): Promise<MiniCourse | undefined>;
  deleteMiniCourse(id: number): Promise<boolean>;
  
  // Quiz operations
  deleteQuiz(id: number): Promise<boolean>;
  
  // AI Credits operations
  deductUserCredits(userId: number, creditsToDeduct: number): Promise<boolean>;
  updateUserAiCredits(userId: number, creditChange: number): Promise<UserStats | undefined>;
  
  // API Keys operations
  getUserApiKeys(userId: number): Promise<UserApiKey[]>;
  getUserApiKeyByService(userId: number, service: string): Promise<UserApiKey | undefined>;
  getUserApiKeyById(id: number): Promise<UserApiKey | undefined>;
  saveUserApiKey(apiKey: InsertUserApiKey): Promise<UserApiKey>;
  updateUserApiKey(id: number, apiKeyUpdate: Partial<UserApiKey>): Promise<UserApiKey | undefined>;
  deleteUserApiKey(id: number): Promise<boolean>;
  verifyUserApiKey(userId: number, service: string, apiKey: string): Promise<boolean>;
  
  // TTS (Text-to-Speech) operations
  saveTTSRecord(record: TTSResponse): Promise<TTSRecord>;
  getTTSHistory(userId: number): Promise<TTSRecord[]>;
  
  // Landing Page operations
  getLandingPage(id: number): Promise<LandingPage | undefined>;
  getLandingPageBySlug(slug: string): Promise<LandingPage | undefined>;
  getLandingPagesByCourseId(courseId: number): Promise<LandingPage[]>;
  getLandingPagesByUserId(userId: number): Promise<LandingPage[]>;
  createLandingPage(landingPage: InsertLandingPage): Promise<LandingPage>;
  updateLandingPage(id: number, landingPage: Partial<LandingPage>): Promise<LandingPage | undefined>;
  publishLandingPage(id: number): Promise<LandingPage | undefined>;
  deleteLandingPage(id: number): Promise<boolean>;
  
  // Landing Page Visit operations
  recordLandingPageVisit(visit: InsertLandingPageVisit): Promise<LandingPageVisit>;
  getLandingPageVisits(landingPageId: number): Promise<LandingPageVisit[]>;
  recordLandingPageConversion(id: number): Promise<LandingPageVisit | undefined>;
  updateLandingPageStats(landingPageId: number): Promise<LandingPage | undefined>;
  getTTSById(id: number): Promise<TTSRecord | undefined>;
  deleteTTSRecord(id: number): Promise<boolean>;

  // Course Draft operations for real-time auto-save
  getCourseDraft(userId: number, draftId: string): Promise<CourseDraft | undefined>;
  getUserCourseDrafts(userId: number): Promise<CourseDraft[]>;
  saveCourseDraft(draft: InsertCourseDraft): Promise<CourseDraft>;
  updateCourseDraft(userId: number, draftId: string, updates: Partial<CourseDraft>): Promise<CourseDraft | undefined>;
  deleteCourseDraft(userId: number, draftId: string): Promise<boolean>;
  cleanupOldCourseDrafts(userId: number, keepCount?: number): Promise<number>;
}

import createMemoryStore from "memorystore";

const MemoryStore = createMemoryStore(session);

export class MemStorage implements IStorage {
  private users: Map<number, User>;
  private courses: Map<number, Course>;
  private modules: Map<number, Module>;
  private lessons: Map<number, Lesson>;
  private media: Map<number, Media>;
  private integrations: Map<number, Integration>;
  private userStatsMap: Map<number, UserStats>;
  private templatesMap: Map<number, Template>;
  private templateHistoryMap: Map<number, TemplateHistory>;
  private teamsMap: Map<number, Team>;
  private teamMembersMap: Map<number, TeamMember[]>;
  private courseCollaboratorsMap: Map<number, CourseCollaborator[]>;
  private teamCoursesMap: Map<number, TeamCourse[]>;
  private ttsRecordsMap: Map<number, TTSRecord>;
  private courseAnalyticsMap: Map<number, CourseAnalytics>;
  private lessonAnalyticsMap: Map<number, LessonAnalytics>;
  private userCourseProgressMap: Map<string, UserCourseProgress>; // key: userId-courseId
  private userLessonProgressMap: Map<string, UserLessonProgress>; // key: userId-lessonId
  private analyticsEventsArray: AnalyticsEvent[];
  private publishingMap: Map<number, Publishing>;
  private aiGeneratedImagesMap: Map<number, AiGeneratedImage>;
  private platformsMap: Map<string, any>; // Platform integrations by slug
  private platformConnectionsMap: Map<string, any>; // key: userId-platformSlug
  private coursePublicationsMap: Map<string, any[]>; // key: userId-courseId
  private miniCoursesMap: Map<number, MiniCourse>;
  private quizzesMap: Map<number, any>; // Quizzes
  private quizQuestionsMap: Map<number, any[]>; // Quiz questions by quiz ID
  private quizAnswersMap: Map<number, any[]>; // Quiz answers by question ID
  private quizFlashcardsMap: Map<number, any[]>; // Quiz flashcards by quiz ID
  private landingPagesMap: Map<number, LandingPage>; // Landing pages 
  private landingPageVisitsMap: Map<number, LandingPageVisit[]>; // Landing page visits by landing page ID
  
  private currentUserId: number;
  private currentCourseId: number;
  private currentModuleId: number;
  private currentLessonId: number;
  private currentMediaId: number;
  private currentIntegrationId: number;
  private currentUserStatsId: number;
  private currentTemplateId: number;
  private currentTemplateHistoryId: number;
  private currentTeamId: number;
  private currentCourseAnalyticsId: number;
  private currentAiGeneratedImageId: number;
  private currentLessonAnalyticsId: number;
  private currentUserCourseProgressId: number;
  private currentUserLessonProgressId: number;
  private currentAnalyticsEventId: number;
  private currentPublishingId: number;
  private currentMiniCourseId: number;
  private currentTTSRecordId: number;
  private currentLandingPageId: number;
  private currentLandingPageVisitId: number;
  private generatedVideosMap: Map<string, GeneratedVideo>;
  
  public sessionStore: session.Store;

  constructor() {
    this.users = new Map();
    this.courses = new Map();
    this.modules = new Map();
    this.lessons = new Map();
    this.media = new Map();
    this.integrations = new Map();
    this.userStatsMap = new Map();
    this.templatesMap = new Map();
    this.templateHistoryMap = new Map();
    this.teamsMap = new Map();
    this.teamMembersMap = new Map();
    this.courseCollaboratorsMap = new Map();
    this.teamCoursesMap = new Map();
    this.ttsRecordsMap = new Map();
    this.courseAnalyticsMap = new Map();
    this.lessonAnalyticsMap = new Map();
    this.userCourseProgressMap = new Map();
    this.userLessonProgressMap = new Map();
    this.analyticsEventsArray = [];
    this.publishingMap = new Map();
    this.aiGeneratedImagesMap = new Map();
    this.generatedVideosMap = new Map<string, GeneratedVideo>();
    this.platformsMap = new Map();
    this.platformConnectionsMap = new Map();
    this.coursePublicationsMap = new Map();
    this.miniCoursesMap = new Map<number, MiniCourse>();
    this.quizzesMap = new Map();
    this.quizQuestionsMap = new Map();
    this.quizAnswersMap = new Map();
    this.quizFlashcardsMap = new Map();
    this.landingPagesMap = new Map<number, LandingPage>();
    this.landingPageVisitsMap = new Map<number, LandingPageVisit[]>();
    
    // Initialize session store
    this.sessionStore = new MemoryStore({
      checkPeriod: 86400000 // prune expired entries every 24h
    });
    
    this.currentUserId = 1;
    this.currentCourseId = 1;
    this.currentModuleId = 1;
    this.currentLessonId = 1;
    this.currentMediaId = 1;
    this.currentIntegrationId = 1;
    this.currentUserStatsId = 1;
    this.currentTemplateId = 1;
    this.currentTemplateHistoryId = 1;
    this.currentTeamId = 1;
    this.currentCourseAnalyticsId = 1;
    this.currentLessonAnalyticsId = 1;
    this.currentUserCourseProgressId = 1;
    this.currentUserLessonProgressId = 1;
    this.currentAnalyticsEventId = 1;
    this.currentPublishingId = 1;
    this.currentAiGeneratedImageId = 1;
    this.currentMiniCourseId = 1;
    this.currentTTSRecordId = 1;
    this.currentLandingPageId = 1;
    this.currentLandingPageVisitId = 1;
    
    // Seed initial templates
    this.seedTemplates();
    
    // Create a demo user
    this.createUser({
      username: "<EMAIL>",
      email: "<EMAIL>",
      password: "password",
      name: "John Smith",
      plan: "Pro",
      role: "user"
    });
    
    // Create demo user stats
    this.createUserStats({
      userId: 1,
      activeCourses: 3,
      publishedCourses: 2,
      aiCredits: 450,
      storageUsed: 24,
      storageLimit: 100
    });
    
    // Create demo integrations
    this.createIntegration({
      userId: 1,
      platform: "udemy",
      status: "active",
      platformUserId: null,
      accessToken: null,
      refreshToken: null,
      tokenExpiry: null,
      config: {
        apiKey: "demo-udemy-api-key",
        domain: "udemy.com"
      }
    });
    
    this.createIntegration({
      userId: 1,
      platform: "teachable",
      status: "active",
      platformUserId: null,
      accessToken: null,
      refreshToken: null,
      tokenExpiry: null,
      config: {
        apiKey: "demo-teachable-api-key",
        domain: "demo-school.teachable.com"
      }
    });
    
    this.createIntegration({
      userId: 1,
      platform: "kajabi",
      status: "expired",
      platformUserId: null,
      accessToken: null,
      refreshToken: null,
      tokenExpiry: null,
      config: {
        apiKey: "demo-kajabi-api-key",
        domain: "demo-site.mykajabi.com"
      }
    });
    
    // Create demo courses
    this.createCourse({
      userId: 1,
      title: "Digital Marketing Fundamentals",
      description: "Learn the basics of digital marketing in this comprehensive course.",
      category: "Marketing",
      status: "published",
      targetAudience: "Beginners",
      thumbnailUrl: ""
    });
    
    this.updateCourse(1, { completion: 100, lessonsCount: 12 });
    
    this.createCourse({
      userId: 1,
      title: "AI for Business Leaders",
      description: "Understand how AI can transform your business operations.",
      category: "Business",
      status: "draft",
      targetAudience: "Intermediate",
      thumbnailUrl: ""
    });
    
    this.updateCourse(2, { completion: 60, lessonsCount: 8 });
    
    this.createCourse({
      userId: 1,
      title: "Python Programming Basics",
      description: "Start your programming journey with Python fundamentals.",
      category: "Technology",
      status: "in_progress",
      targetAudience: "Beginners",
      thumbnailUrl: ""
    });
    
    this.updateCourse(3, { completion: 25, lessonsCount: 15 });
  }

  private seedTemplates() {
    const templates = [
      // AI Templates
      {
        name: "Quick Course Generator",
        description: "Generate a complete course structure from just a title and description",
        icon: "ri-robot-line",
        type: "course_generator"
      },
      {
        name: "Script Writer",
        description: "Create engaging lesson scripts with AI assistance",
        icon: "ri-file-text-line",
        type: "script_generator"
      },
      {
        name: "AI Voiceover",
        description: "Convert your scripts to natural-sounding voiceovers",
        icon: "ri-voice-recognition-line",
        type: "voice_generator"
      },
      
      // Course Templates
      {
        name: "Marketing Master Class",
        description: "Professional template for digital marketing courses",
        icon: "ri-flight-takeoff-line",
        type: "course_template",
        category: "marketing",
        structure: {
          modules: [
            {
              title: "Marketing Fundamentals",
              description: "Core concepts and principles of modern digital marketing",
              lessons: [
                {
                  title: "Introduction to Digital Marketing",
                  description: "Overview of digital marketing channels and strategies"
                },
                {
                  title: "Developing a Marketing Strategy",
                  description: "How to create an effective digital marketing plan"
                },
                {
                  title: "Understanding Your Target Audience",
                  description: "Techniques for audience research and persona development"
                }
              ]
            },
            {
              title: "Content Marketing",
              description: "Creating valuable content that drives engagement",
              lessons: [
                {
                  title: "Content Strategy Fundamentals",
                  description: "Planning content that aligns with business goals"
                },
                {
                  title: "Creating Engaging Content",
                  description: "Techniques for creating compelling digital content"
                },
                {
                  title: "Content Distribution Channels",
                  description: "Where and how to promote your content effectively"
                }
              ]
            },
            {
              title: "Social Media Marketing",
              description: "Leveraging social platforms for business growth",
              lessons: [
                {
                  title: "Social Media Strategy",
                  description: "Building an effective social media presence"
                },
                {
                  title: "Platform-Specific Tactics",
                  description: "Optimizing content for different social networks"
                },
                {
                  title: "Social Media Analytics",
                  description: "Measuring and improving social media performance"
                }
              ]
            },
            {
              title: "Advertising & Promotion",
              description: "Paid marketing strategies for audience growth",
              lessons: [
                {
                  title: "PPC Advertising Fundamentals",
                  description: "Introduction to paid search and display ads"
                },
                {
                  title: "Social Media Advertising",
                  description: "Creating effective paid campaigns on social platforms"
                },
                {
                  title: "Measuring Ad Performance",
                  description: "Analytics and optimization for paid campaigns"
                }
              ]
            }
          ]
        }
      },
      {
        name: "Programming Fundamentals",
        description: "Template for teaching coding concepts to beginners",
        icon: "ri-code-line",
        type: "course_template",
        category: "programming",
        structure: {
          modules: [
            {
              title: "Introduction to Programming",
              description: "Basic programming concepts and environment setup",
              lessons: [
                {
                  title: "What is Programming?",
                  description: "Introduction to programming concepts and logic"
                },
                {
                  title: "Setting Up Your Development Environment",
                  description: "Tools and software needed to start programming"
                },
                {
                  title: "Basic Syntax and Structure",
                  description: "Understanding code organization and syntax rules"
                }
              ]
            },
            {
              title: "Data Types and Variables",
              description: "Working with different types of data in programming",
              lessons: [
                {
                  title: "Variables and Constants",
                  description: "How to store and manipulate data in your code"
                },
                {
                  title: "Numbers, Strings, and Booleans",
                  description: "Working with primitive data types"
                },
                {
                  title: "Arrays and Collections",
                  description: "Storing and accessing multiple values"
                }
              ]
            },
            {
              title: "Control Structures",
              description: "Making decisions and controlling flow in programs",
              lessons: [
                {
                  title: "Conditional Statements",
                  description: "Using if, else if, and else for decision making"
                },
                {
                  title: "Loops and Iterations",
                  description: "Repeating actions with for and while loops"
                },
                {
                  title: "Switch Statements",
                  description: "Alternative to multiple if-else conditions"
                }
              ]
            },
            {
              title: "Functions and Methods",
              description: "Creating reusable blocks of code",
              lessons: [
                {
                  title: "Function Basics",
                  description: "Defining and calling functions"
                },
                {
                  title: "Parameters and Return Values",
                  description: "Passing data to and from functions"
                },
                {
                  title: "Scope and Lifetime",
                  description: "Understanding variable scope in functions"
                }
              ]
            }
          ]
        }
      },
      {
        name: "Personal Development",
        description: "Template for courses on self-improvement and growth",
        icon: "ri-mental-health-line",
        type: "course_template",
        category: "personal_development",
        structure: {
          modules: [
            {
              title: "Self-Awareness & Mindset",
              description: "Understanding yourself and developing a growth mindset",
              lessons: [
                {
                  title: "Understanding Your Core Values",
                  description: "Identifying what matters most to you personally"
                },
                {
                  title: "Fixed vs. Growth Mindset",
                  description: "How your beliefs about ability affect your success"
                },
                {
                  title: "Self-Assessment Techniques",
                  description: "Tools for understanding your strengths and weaknesses"
                }
              ]
            },
            {
              title: "Goal Setting & Achievement",
              description: "Creating and reaching meaningful personal goals",
              lessons: [
                {
                  title: "SMART Goal Framework",
                  description: "Setting specific, measurable, achievable, relevant, and time-bound goals"
                },
                {
                  title: "Creating Action Plans",
                  description: "Breaking down large goals into manageable steps"
                },
                {
                  title: "Overcoming Obstacles",
                  description: "Strategies for pushing through challenges and setbacks"
                }
              ]
            },
            {
              title: "Productivity & Time Management",
              description: "Maximizing your effectiveness and efficiency",
              lessons: [
                {
                  title: "Time Audit & Prioritization",
                  description: "Understanding where your time goes and focusing on what matters"
                },
                {
                  title: "Productivity Systems",
                  description: "Frameworks and methods for getting more done"
                },
                {
                  title: "Beating Procrastination",
                  description: "Psychology and strategies to overcome delay"
                }
              ]
            },
            {
              title: "Emotional Intelligence",
              description: "Developing awareness and management of emotions",
              lessons: [
                {
                  title: "Understanding Emotions",
                  description: "Recognizing and naming your emotional states"
                },
                {
                  title: "Emotional Regulation",
                  description: "Techniques for managing strong emotions effectively"
                },
                {
                  title: "Empathy & Social Awareness",
                  description: "Understanding others' perspectives and emotions"
                }
              ]
            }
          ]
        }
      },
      {
        name: "Design Masterclass",
        description: "Template for courses on graphic design and UI/UX",
        icon: "ri-palette-line",
        type: "course_template",
        category: "design",
        structure: {
          modules: [
            {
              title: "Design Fundamentals",
              description: "Core principles of visual design and aesthetics",
              lessons: [
                {
                  title: "Elements of Design",
                  description: "Understanding line, shape, color, texture, space and form"
                },
                {
                  title: "Principles of Design",
                  description: "Balance, contrast, hierarchy, pattern, rhythm, and unity"
                },
                {
                  title: "Color Theory",
                  description: "Understanding color relationships and psychology"
                }
              ]
            },
            {
              title: "Typography & Layout",
              description: "Working with text and arranging design elements",
              lessons: [
                {
                  title: "Typography Basics",
                  description: "Type anatomy, classifications, and selection principles"
                },
                {
                  title: "Grid Systems",
                  description: "Creating strong structure in your designs"
                },
                {
                  title: "Composition Techniques",
                  description: "Arranging elements for visual impact"
                }
              ]
            },
            {
              title: "UI/UX Design",
              description: "Creating effective digital interfaces",
              lessons: [
                {
                  title: "User Experience Fundamentals",
                  description: "Understanding user-centered design approaches"
                },
                {
                  title: "Interface Design Patterns",
                  description: "Common solutions to UI/UX challenges"
                },
                {
                  title: "Prototyping & Testing",
                  description: "Validating designs with user feedback"
                }
              ]
            },
            {
              title: "Digital Design Tools",
              description: "Mastering professional design software",
              lessons: [
                {
                  title: "Vector Graphics",
                  description: "Working with paths, shapes and illustrations"
                },
                {
                  title: "Raster Image Editing",
                  description: "Photo manipulation and digital painting"
                },
                {
                  title: "Design Collaboration",
                  description: "Working effectively in design teams"
                }
              ]
            }
          ]
        }
      },
      {
        name: "Business & Entrepreneurship",
        description: "Template for courses on starting and growing businesses",
        icon: "ri-building-line",
        type: "course_template",
        category: "business",
        structure: {
          modules: [
            {
              title: "Business Foundations",
              description: "Core concepts for starting and running a business",
              lessons: [
                {
                  title: "Identifying Business Opportunities",
                  description: "How to recognize and evaluate potential markets"
                },
                {
                  title: "Business Models & Revenue Streams",
                  description: "Different approaches to creating value and generating revenue"
                },
                {
                  title: "Legal Structures & Compliance",
                  description: "Understanding business entities and regulatory requirements"
                }
              ]
            },
            {
              title: "Market Research & Strategy",
              description: "Understanding your customers and competitive landscape",
              lessons: [
                {
                  title: "Customer Discovery & Validation",
                  description: "Methods for identifying and understanding your target audience"
                },
                {
                  title: "Competitive Analysis",
                  description: "Evaluating the market landscape and competitive positioning"
                },
                {
                  title: "Creating a Business Plan",
                  description: "Developing a roadmap for your business success"
                }
              ]
            },
            {
              title: "Marketing & Sales",
              description: "Attracting customers and generating revenue",
              lessons: [
                {
                  title: "Digital Marketing Fundamentals",
                  description: "Essential online marketing channels and strategies"
                },
                {
                  title: "Sales Techniques & Processes",
                  description: "Effective approaches to convert leads into customers"
                },
                {
                  title: "Customer Relationship Management",
                  description: "Building long-term value through customer retention"
                }
              ]
            },
            {
              title: "Financial Management",
              description: "Managing money and planning for growth",
              lessons: [
                {
                  title: "Financial Statements & Accounting",
                  description: "Understanding the numbers that drive your business"
                },
                {
                  title: "Pricing Strategies",
                  description: "Determining optimal pricing for profitability"
                },
                {
                  title: "Funding & Investment",
                  description: "Options for financing business growth"
                }
              ]
            }
          ]
        }
      },
      {
        name: "Health & Fitness",
        description: "Template for wellness and fitness education",
        icon: "ri-heart-pulse-line",
        type: "course_template",
        category: "health",
        structure: {
          modules: [
            {
              title: "Fitness Fundamentals",
              description: "Core concepts of physical fitness and exercise science",
              lessons: [
                {
                  title: "Understanding Fitness Components",
                  description: "Cardiovascular endurance, muscular strength, flexibility, and body composition"
                },
                {
                  title: "Exercise Physiology Basics",
                  description: "How your body responds and adapts to physical activity"
                },
                {
                  title: "Creating a Balanced Fitness Plan",
                  description: "Designing effective and sustainable workout routines"
                }
              ]
            },
            {
              title: "Nutrition & Diet",
              description: "Fueling your body for optimal health and performance",
              lessons: [
                {
                  title: "Macronutrients & Micronutrients",
                  description: "Understanding proteins, carbohydrates, fats, vitamins, and minerals"
                },
                {
                  title: "Meal Planning & Preparation",
                  description: "Creating balanced, nutritious meals for health goals"
                },
                {
                  title: "Hydration & Supplements",
                  description: "Water needs and supplement considerations"
                }
              ]
            },
            {
              title: "Mental Wellness",
              description: "Building psychological resilience and mindfulness",
              lessons: [
                {
                  title: "Stress Management Techniques",
                  description: "Practical approaches to reduce and manage stress"
                },
                {
                  title: "Sleep Optimization",
                  description: "Improving sleep quality for better health"
                },
                {
                  title: "Mindfulness Practices",
                  description: "Meditation and present-moment awareness"
                }
              ]
            },
            {
              title: "Specialized Training",
              description: "Techniques for specific fitness objectives",
              lessons: [
                {
                  title: "Strength Training Principles",
                  description: "Building muscle and increasing strength safely"
                },
                {
                  title: "Cardiovascular Training Methods",
                  description: "Improving heart health and endurance"
                },
                {
                  title: "Flexibility & Mobility Work",
                  description: "Enhancing range of motion and preventing injury"
                }
              ]
            }
          ]
        }
      },
      
      // Language Learning template
      {
        name: "Language Learning",
        description: "Template for language acquisition courses",
        icon: "ri-translate-2",
        type: "course_template",
        category: "languages",
        structure: {
          modules: [
            {
              title: "Pronunciation & Basic Vocabulary",
              description: "Master the sounds and essential words of the language",
              lessons: [
                {
                  title: "Alphabet and Sound System",
                  description: "Learning the fundamental sounds and letters"
                },
                {
                  title: "Basic Greetings and Introductions",
                  description: "How to introduce yourself and greet others"
                },
                {
                  title: "Essential Everyday Vocabulary",
                  description: "Common words for daily communication"
                }
              ]
            },
            {
              title: "Grammar Foundations",
              description: "Core grammatical structures for basic communication",
              lessons: [
                {
                  title: "Sentence Structure",
                  description: "How to form basic sentences correctly"
                },
                {
                  title: "Verb Conjugation Basics",
                  description: "Understanding present tense forms"
                },
                {
                  title: "Question Formation",
                  description: "How to ask different types of questions"
                }
              ]
            },
            {
              title: "Conversation Skills",
              description: "Practical speaking and listening practice",
              lessons: [
                {
                  title: "Everyday Conversations",
                  description: "Common dialogues for real-life situations"
                },
                {
                  title: "Active Listening Strategies",
                  description: "How to understand native speakers"
                },
                {
                  title: "Speaking Fluency Practice",
                  description: "Exercises to improve speaking confidence"
                }
              ]
            },
            {
              title: "Cultural Context",
              description: "Understanding the culture behind the language",
              lessons: [
                {
                  title: "Cultural Norms and Etiquette",
                  description: "Appropriate behaviors and customs"
                },
                {
                  title: "Idioms and Expressions",
                  description: "Common sayings and their meanings"
                },
                {
                  title: "Media and Literature",
                  description: "Exposure to authentic language materials"
                }
              ]
            }
          ]
        }
      },
      
      // Corporate Training template
      {
        name: "Corporate Training",
        description: "Template for professional development programs",
        icon: "ri-building-line",
        type: "course_template",
        category: "professional",
        structure: {
          modules: [
            {
              title: "Leadership Skills",
              description: "Developing effective leadership capabilities",
              lessons: [
                {
                  title: "Leadership Styles and Approaches",
                  description: "Understanding different leadership frameworks"
                },
                {
                  title: "Effective Team Management",
                  description: "Strategies for leading high-performing teams"
                },
                {
                  title: "Decision Making and Problem Solving",
                  description: "Analytical approaches to business challenges"
                }
              ]
            },
            {
              title: "Communication in the Workplace",
              description: "Enhancing professional communication skills",
              lessons: [
                {
                  title: "Presentation Skills",
                  description: "Delivering impactful business presentations"
                },
                {
                  title: "Email and Written Communication",
                  description: "Professional writing for business contexts"
                },
                {
                  title: "Conflict Resolution",
                  description: "Addressing and resolving workplace disputes"
                }
              ]
            },
            {
              title: "Project Management",
              description: "Planning and executing business projects",
              lessons: [
                {
                  title: "Project Planning and Scope",
                  description: "Defining clear project parameters"
                },
                {
                  title: "Resource Allocation and Scheduling",
                  description: "Optimizing time and resources"
                },
                {
                  title: "Risk Management",
                  description: "Identifying and mitigating potential issues"
                }
              ]
            },
            {
              title: "Professional Development",
              description: "Personal growth in the business environment",
              lessons: [
                {
                  title: "Career Planning",
                  description: "Strategizing for professional advancement"
                },
                {
                  title: "Networking and Relationship Building",
                  description: "Creating valuable professional connections"
                },
                {
                  title: "Work-Life Balance",
                  description: "Maintaining wellbeing in demanding environments"
                }
              ]
            }
          ]
        }
      },
      
      // Finance Education template
      {
        name: "Financial Education",
        description: "Template for personal and business finance courses",
        icon: "ri-money-dollar-circle-line",
        type: "course_template",
        category: "finance",
        structure: {
          modules: [
            {
              title: "Financial Foundations",
              description: "Essential concepts and principles of finance",
              lessons: [
                {
                  title: "Money Fundamentals",
                  description: "Understanding currency, value, and financial systems"
                },
                {
                  title: "Income and Expense Management",
                  description: "Tracking and optimizing cash flow"
                },
                {
                  title: "Financial Goal Setting",
                  description: "Defining and prioritizing financial objectives"
                }
              ]
            },
            {
              title: "Saving and Investing",
              description: "Growing wealth through strategic allocation",
              lessons: [
                {
                  title: "Saving Strategies",
                  description: "Building emergency funds and saving for goals"
                },
                {
                  title: "Investment Basics",
                  description: "Understanding stocks, bonds, and other assets"
                },
                {
                  title: "Risk Management",
                  description: "Balancing potential returns with risk tolerance"
                }
              ]
            },
            {
              title: "Credit and Debt",
              description: "Managing borrowing and leveraging credit effectively",
              lessons: [
                {
                  title: "Understanding Credit Scores",
                  description: "What impacts your credit and why it matters"
                },
                {
                  title: "Smart Debt Management",
                  description: "Strategies for eliminating and leveraging debt"
                },
                {
                  title: "Loan Types and Considerations",
                  description: "Comparing different financing options"
                }
              ]
            },
            {
              title: "Financial Planning",
              description: "Creating comprehensive strategies for financial success",
              lessons: [
                {
                  title: "Retirement Planning",
                  description: "Preparing for financial independence"
                },
                {
                  title: "Tax Planning Strategies",
                  description: "Minimizing tax burden legally and effectively"
                },
                {
                  title: "Estate Planning Basics",
                  description: "Protecting and transferring assets"
                }
              ]
            }
          ]
        }
      }
    ];
    
    templates.forEach(template => {
      const id = this.currentTemplateId++;
      // Ensure all template items have proper types for consistent interface
      this.templatesMap.set(id, { 
        ...template, 
        id,
        category: template.category || null,
        structure: template.structure || null
      });
    });
  }

  // User operations
  async getUser(id: number): Promise<User | undefined> {
    return this.users.get(id);
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    return Array.from(this.users.values()).find(
      (user) => user.username === username,
    );
  }
  
  async getAllUsers(): Promise<User[]> {
    return Array.from(this.users.values());
  }
  
  async getUserByEmail(email: string): Promise<User | undefined> {
    return Array.from(this.users.values()).find(
      (user) => user.email === email,
    );
  }
  
  async setPasswordResetToken(email: string): Promise<string | null> {
    const user = await this.getUserByEmail(email);
    
    if (!user) {
      return null;
    }
    
    // Generate a random token
    const resetToken = crypto.randomBytes(32).toString('hex');
    
    // Set expiration to 1 hour from now
    const resetPasswordExpiry = new Date();
    resetPasswordExpiry.setHours(resetPasswordExpiry.getHours() + 1);
    
    // Update user with reset token and expiry
    await this.updateUser(user.id, {
      resetPasswordToken: resetToken,
      resetPasswordExpiry
    });
    
    return resetToken;
  }
  
  async getUserByResetToken(token: string): Promise<User | undefined> {
    const user = Array.from(this.users.values()).find(
      (user) => user.resetPasswordToken === token
    );
    
    if (!user) {
      return undefined;
    }
    
    // Check if token is expired
    if (user.resetPasswordExpiry && new Date() > new Date(user.resetPasswordExpiry)) {
      // Token is expired, clear it
      await this.updateUser(user.id, {
        resetPasswordToken: null,
        resetPasswordExpiry: null
      });
      return undefined;
    }
    
    return user;
  }
  
  async resetPassword(token: string, newPassword: string): Promise<boolean> {
    const user = await this.getUserByResetToken(token);
    
    if (!user) {
      return false;
    }
    
    // Hash the new password
    const hashedPassword = await AuthService.hashPassword(newPassword);
    
    // Update user with new password and clear reset token
    await this.updateUser(user.id, {
      password: hashedPassword,
      resetPasswordToken: null,
      resetPasswordExpiry: null
    });
    
    return true;
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const id = this.currentUserId++;
    const now = new Date();
    
    // Create a complete user object with all required fields
    const user: User = {
      ...insertUser,
      id,
      emailVerified: false,
      verificationToken: null,
      resetPasswordToken: null,
      resetPasswordExpiry: null,
      avatarUrl: insertUser.avatarUrl || null,
      plan: insertUser.plan || "free",
      role: insertUser.role || "user",
      stripeCustomerId: insertUser.stripeCustomerId || null,
      stripeSubscriptionId: insertUser.stripeSubscriptionId || null,
      createdAt: now
    };
    
    this.users.set(id, user);
    return user;
  }
  
  async updateUser(id: number, userUpdate: Partial<User>): Promise<User | undefined> {
    const user = this.users.get(id);
    if (!user) return undefined;
    
    // Don't allow updating sensitive fields directly
    const { password, verificationToken, resetPasswordToken, resetPasswordExpiry, ...safeUpdate } = userUpdate;
    
    const updatedUser = { ...user, ...safeUpdate };
    this.users.set(id, updatedUser);
    
    return updatedUser;
  }
  
  async updateStripeCustomerId(userId: number, customerId: string): Promise<User | undefined> {
    const user = this.users.get(userId);
    if (!user) return undefined;
    
    const updatedUser = { ...user, stripeCustomerId: customerId };
    this.users.set(userId, updatedUser);
    
    return updatedUser;
  }
  
  async updateUserStripeInfo(userId: number, stripeInfo: { customerId: string, subscriptionId: string }): Promise<User | undefined> {
    const user = this.users.get(userId);
    if (!user) return undefined;
    
    const updatedUser = { 
      ...user, 
      stripeCustomerId: stripeInfo.customerId,
      stripeSubscriptionId: stripeInfo.subscriptionId
    };
    
    this.users.set(userId, updatedUser);
    
    return updatedUser;
  }
  
  // Course operations
  async getCourse(id: number): Promise<Course | undefined> {
    return this.courses.get(id);
  }
  
  async getCoursesByUserId(userId: number): Promise<Course[]> {
    return Array.from(this.courses.values()).filter(
      course => course.userId === userId
    );
  }
  
  async createCourse(insertCourse: InsertCourse): Promise<Course> {
    const id = this.currentCourseId++;
    const now = new Date();
    
    // Create a complete course object with all required fields
    const course: Course = { 
      ...insertCourse, 
      id, 
      status: insertCourse.status || "draft",
      targetAudience: insertCourse.targetAudience || null,
      thumbnailUrl: insertCourse.thumbnailUrl || null,
      completion: 0,
      lessonsCount: 0,
      createdAt: now,
      updatedAt: now
    };
    
    this.courses.set(id, course);
    
    // Update user stats
    const stats = await this.getUserStats(course.userId);
    if (stats) {
      await this.updateUserStats(course.userId, { 
        activeCourses: (stats.activeCourses || 0) + 1,
        publishedCourses: course.status === "published" ? (stats.publishedCourses || 0) + 1 : (stats.publishedCourses || 0)
      });
    }
    
    return course;
  }
  
  async updateCourse(id: number, courseUpdate: Partial<Course>): Promise<Course | undefined> {
    const course = this.courses.get(id);
    if (!course) return undefined;
    
    const updatedCourse = { ...course, ...courseUpdate, updatedAt: new Date() };
    this.courses.set(id, updatedCourse);
    
    // Update user stats if status changed to published
    if (courseUpdate.status === "published" && course.status !== "published") {
      const stats = await this.getUserStats(course.userId);
      if (stats) {
        await this.updateUserStats(course.userId, { 
          publishedCourses: (stats.publishedCourses || 0) + 1
        });
      }
    }
    
    return updatedCourse;
  }
  
  async deleteCourse(id: number): Promise<boolean> {
    const course = this.courses.get(id);
    if (!course) return false;
    
    // Delete all lessons associated with this course
    const courseLessons = await this.getLessonsByCourseId(id);
    for (const lesson of courseLessons) {
      await this.deleteLesson(lesson.id);
    }
    
    // Update user stats
    const stats = await this.getUserStats(course.userId);
    if (stats) {
      await this.updateUserStats(course.userId, { 
        activeCourses: Math.max(0, (stats.activeCourses || 0) - 1),
        publishedCourses: course.status === "published" ? Math.max(0, (stats.publishedCourses || 0) - 1) : (stats.publishedCourses || 0)
      });
    }
    
    return this.courses.delete(id);
  }
  
  // Module operations
  async getModule(id: number): Promise<Module | undefined> {
    return this.modules.get(id);
  }

  async deleteModule(id: number): Promise<boolean> {
    if (!this.modules.has(id)) {
      return false;
    }
    return this.modules.delete(id);
  }

  async getModulesByCourseId(courseId: number): Promise<Module[]> {
    return Array.from(this.modules.values())
      .filter(module => module.courseId === courseId)
      .sort((a, b) => a.order - b.order);
  }

  async createModule(insertModule: InsertModule): Promise<Module> {
    const id = this.currentModuleId++;
    const now = new Date();
    const module: Module = {
      id,
      ...insertModule,
      status: insertModule.status || "active",
      description: insertModule.description || null,
      createdAt: now,
      updatedAt: now
    };
    this.modules.set(id, module);
    return module;
  }

  async updateModule(id: number, moduleUpdate: Partial<Module>): Promise<Module | undefined> {
    const module = this.modules.get(id);
    if (!module) {
      return undefined;
    }
    
    const updatedModule = {
      ...module,
      ...moduleUpdate,
      updatedAt: new Date()
    };
    
    this.modules.set(id, updatedModule);
    return updatedModule;
  }

  async deleteModule(id: number): Promise<boolean> {
    return this.modules.delete(id);
  }
  
  // Lesson operations
  async getLesson(id: number): Promise<Lesson | undefined> {
    return this.lessons.get(id);
  }
  
  async getLessonsByCourseId(courseId: number): Promise<Lesson[]> {
    return Array.from(this.lessons.values())
      .filter(lesson => lesson.courseId === courseId)
      .sort((a, b) => a.order - b.order);
  }
  
  async getLessonsByModuleId(moduleId: number): Promise<Lesson[]> {
    return Array.from(this.lessons.values())
      .filter(lesson => lesson.moduleId === moduleId)
      .sort((a, b) => a.order - b.order);
  }
  
  async createLesson(insertLesson: InsertLesson): Promise<Lesson> {
    const id = this.currentLessonId++;
    
    // Create a complete lesson object with all required fields
    const lesson: Lesson = { 
      ...insertLesson, 
      id, 
      status: insertLesson.status || "draft",
      description: insertLesson.description || null,
      script: insertLesson.script || null,
      videoUrl: insertLesson.videoUrl || null,
      voiceoverId: insertLesson.voiceoverId || null,
      duration: 0,
      // Default values for micro-learning fields
      microLearningEnabled: insertLesson.microLearningEnabled === undefined ? false : insertLesson.microLearningEnabled,
      microLearningSegmentCount: insertLesson.microLearningSegmentCount === undefined ? 1 : insertLesson.microLearningSegmentCount,
      microLearningBreakInterval: insertLesson.microLearningBreakInterval === undefined ? 300 : insertLesson.microLearningBreakInterval,
      microLearningBreakDuration: insertLesson.microLearningBreakDuration === undefined ? 60 : insertLesson.microLearningBreakDuration
    };
    
    this.lessons.set(id, lesson);
    
    // Update course lessons count
    const course = await this.getCourse(lesson.courseId);
    if (course) {
      await this.updateCourse(course.id, { lessonsCount: (course.lessonsCount || 0) + 1 });
    }
    
    return lesson;
  }
  
  async updateLesson(id: number, lessonUpdate: Partial<Lesson>): Promise<Lesson | undefined> {
    const lesson = this.lessons.get(id);
    if (!lesson) return undefined;
    
    const updatedLesson = { ...lesson, ...lessonUpdate };
    this.lessons.set(id, updatedLesson);
    
    return updatedLesson;
  }
  
  async deleteLesson(id: number): Promise<boolean> {
    const lesson = this.lessons.get(id);
    if (!lesson) return false;
    
    // Update course lessons count
    const course = await this.getCourse(lesson.courseId);
    if (course) {
      await this.updateCourse(course.id, { lessonsCount: Math.max(0, (course.lessonsCount || 0) - 1) });
    }
    
    return this.lessons.delete(id);
  }
  
  // Media operations
  async getMedia(id: number): Promise<Media | undefined> {
    return this.media.get(id);
  }
  
  async getMediaByUserId(userId: number): Promise<Media[]> {
    return Array.from(this.media.values()).filter(
      media => media.userId === userId
    );
  }
  
  async getMediaById(id: number): Promise<Media | undefined> {
    return this.media.get(id);
  }
  
  async getMediaBySourceId(sourceId: string): Promise<Media | undefined> {
    return Array.from(this.media.values()).find(
      media => media.sourceId === sourceId
    );
  }
  
  async getRandomMedia(count: number, types?: string[]): Promise<Media[]> {
    let mediaItems = [...this.media.values()];
    
    // Filter by type if specified
    if (types && types.length > 0) {
      mediaItems = mediaItems.filter(item => types.includes(item.type));
    }
    
    // Get random items
    const randomItems: Media[] = [];
    const totalItems = mediaItems.length;
    
    if (totalItems === 0) return [];
    
    // Get up to 'count' random items without duplicates
    const indices = new Set<number>();
    while (indices.size < Math.min(count, totalItems)) {
      indices.add(Math.floor(Math.random() * totalItems));
    }
    
    // Convert indices to media items
    indices.forEach(index => {
      randomItems.push(mediaItems[index]);
    });
    
    return randomItems;
  }
  
  async getUserMedia(userId: number): Promise<Media[]> {
    return Array.from(this.media.values()).filter(
      media => media.userId === userId
    );
  }

  async createMedia(insertMedia: InsertMedia): Promise<Media> {
    const id = this.currentMediaId++;
    
    // Create a properly typed Media object with all required fields
    const media: Media = {
      id,
      userId: insertMedia.userId,
      name: insertMedia.name,
      type: insertMedia.type,
      mimeType: insertMedia.mimeType || 'application/octet-stream',
      fileSize: insertMedia.fileSize || 0,
      url: insertMedia.url,
      originalFilename: insertMedia.originalFilename || insertMedia.name,
      duration: insertMedia.duration || null,
      courseId: insertMedia.courseId || null,
      lessonId: insertMedia.lessonId || null,
      createdAt: new Date(),
      source: insertMedia.source || 'upload',
      sourceId: insertMedia.sourceId || null,
      sourceData: insertMedia.sourceData || null
    };
    
    this.media.set(id, media);
    
    // Update user storage stats if we have a file size
    if (insertMedia.fileSize && insertMedia.fileSize > 0) {
      const stats = await this.getUserStats(media.userId);
      if (stats) {
        const fileSizeKB = Math.ceil(insertMedia.fileSize / 1024); // Convert bytes to KB
        await this.updateUserStats(media.userId, { 
          storageUsed: (stats.storageUsed || 0) + fileSizeKB
        });
      }
    }
    
    return media;
  }
  
  async deleteMedia(id: number): Promise<boolean> {
    const media = this.media.get(id);
    if (!media) return false;
    
    // Update user storage stats
    const stats = await this.getUserStats(media.userId);
    if (stats) {
      await this.updateUserStats(media.userId, { 
        storageUsed: Math.max(0, (stats.storageUsed || 0) - 1)
      });
    }
    
    return this.media.delete(id);
  }
  
  async updateMedia(id: number, updates: Partial<Media>): Promise<Media | undefined> {
    const media = this.media.get(id);
    if (!media) return undefined;
    
    // Create updated media object
    const updatedMedia = {
      ...media,
      ...updates
    };
    
    // Save updated media
    this.media.set(id, updatedMedia);
    
    return updatedMedia;
  }
  
  // Integration operations
  async getIntegrationsByUserId(userId: number): Promise<Integration[]> {
    return Array.from(this.integrations.values()).filter(
      integration => integration.userId === userId
    );
  }
  
  async createIntegration(insertIntegration: InsertIntegration): Promise<Integration> {
    const id = this.currentIntegrationId++;
    const now = new Date();
    
    const integration: Integration = { 
      ...insertIntegration, 
      id,
      platformUserId: insertIntegration.platformUserId || null,
      accessToken: insertIntegration.accessToken || null,
      refreshToken: insertIntegration.refreshToken || null,
      tokenExpiry: insertIntegration.tokenExpiry || null,
      config: insertIntegration.config || {},
      status: insertIntegration.status || 'active',
      createdAt: now,
      updatedAt: now
    };
    
    this.integrations.set(id, integration);
    return integration;
  }
  
  async updateIntegration(id: number, integrationUpdate: Partial<Integration>): Promise<Integration | undefined> {
    const integration = this.integrations.get(id);
    if (!integration) return undefined;
    
    const now = new Date();
    const updatedIntegration = { 
      ...integration, 
      ...integrationUpdate,
      updatedAt: now
    };
    
    this.integrations.set(id, updatedIntegration);
    return updatedIntegration;
  }
  
  async deleteIntegration(id: number): Promise<boolean> {
    return this.integrations.delete(id);
  }
  
  // User stats operations
  async getUserStats(userId: number): Promise<UserStats | undefined> {
    return Array.from(this.userStatsMap.values()).find(
      stats => stats.userId === userId
    );
  }
  
  async createUserStats(insertStats: InsertUserStats): Promise<UserStats> {
    const id = this.currentUserStatsId++;
    const stats: UserStats = {
      id,
      userId: insertStats.userId,
      activeCourses: insertStats.activeCourses !== undefined ? insertStats.activeCourses : 0,
      publishedCourses: insertStats.publishedCourses !== undefined ? insertStats.publishedCourses : 0,
      aiCredits: insertStats.aiCredits !== undefined ? insertStats.aiCredits : 500,
      storageUsed: insertStats.storageUsed !== undefined ? insertStats.storageUsed : 0,
      storageLimit: insertStats.storageLimit !== undefined ? insertStats.storageLimit : 1000
    };
    this.userStatsMap.set(id, stats);
    return stats;
  }
  
  async updateUserStats(userId: number, statsUpdate: Partial<UserStats>): Promise<UserStats | undefined> {
    const stats = await this.getUserStats(userId);
    if (!stats) return undefined;
    
    const updatedStats = { ...stats, ...statsUpdate };
    this.userStatsMap.set(stats.id, updatedStats);
    
    return updatedStats;
  }
  
  // Template operations
  async getAllTemplates(): Promise<Template[]> {
    const templates = Array.from(this.templatesMap.values());
    
    // If no templates exist, seed them first
    if (templates.length === 0) {
      this.seedTemplates();
      return Array.from(this.templatesMap.values());
    }
    
    return templates;
  }
  
  async getTemplate(id: number): Promise<Template | undefined> {
    // If template doesn't exist, check if templates need to be seeded
    if (!this.templatesMap.has(id) && this.templatesMap.size === 0) {
      this.seedTemplates();
    }
    
    return this.templatesMap.get(id);
  }
  
  // Template History operations
  async getTemplateHistory(id: number): Promise<TemplateHistory | undefined> {
    return this.templateHistoryMap.get(id);
  }
  
  async getTemplateHistoryByUserId(userId: number): Promise<TemplateHistory[]> {
    return Array.from(this.templateHistoryMap.values())
      .filter(history => history.userId === userId)
      .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
  }
  
  async createTemplateHistory(insertHistory: InsertTemplateHistory): Promise<TemplateHistory> {
    const id = this.currentTemplateHistoryId++;
    const now = new Date();
    
    const templateHistory: TemplateHistory = {
      ...insertHistory,
      id,
      createdAt: now,
      aiCreditsUsed: insertHistory.aiCreditsUsed || 0,
      favorited: insertHistory.favorited || false
    };
    
    this.templateHistoryMap.set(id, templateHistory);
    
    // Update user AI credits if applicable
    if (insertHistory.aiCreditsUsed && insertHistory.aiCreditsUsed > 0) {
      const stats = await this.getUserStats(insertHistory.userId);
      if (stats) {
        await this.updateUserStats(insertHistory.userId, {
          aiCredits: Math.max(0, (stats.aiCredits || 0) - insertHistory.aiCreditsUsed)
        });
      }
    }
    
    return templateHistory;
  }
  
  async updateTemplateHistoryFavorite(id: number, favorited: boolean): Promise<TemplateHistory | undefined> {
    const history = this.templateHistoryMap.get(id);
    if (!history) return undefined;
    
    const updatedHistory = { ...history, favorited };
    this.templateHistoryMap.set(id, updatedHistory);
    
    return updatedHistory;
  }
  
  // Team operations
  async getTeam(id: number): Promise<Team | undefined> {
    return this.teamsMap.get(id);
  }
  
  async getTeamsByUserId(userId: number): Promise<Team[]> {
    // Get teams where user is owner or member
    const ownedTeams = Array.from(this.teamsMap.values()).filter(
      team => team.ownerId === userId
    );
    
    const memberTeams: Team[] = [];
    
    // Find teams where the user is a member
    this.teamMembersMap.forEach((members, teamId) => {
      if (members.some(member => member.userId === userId)) {
        const team = this.teamsMap.get(teamId);
        if (team) memberTeams.push(team);
      }
    });
    
    return [...ownedTeams, ...memberTeams];
  }
  
  async createTeam(insertTeam: InsertTeam): Promise<Team> {
    const id = this.currentTeamId++;
    const now = new Date();
    
    const team: Team = {
      ...insertTeam,
      id,
      avatarUrl: insertTeam.avatarUrl || null,
      description: insertTeam.description || null,
      createdAt: now,
      updatedAt: now
    };
    
    this.teamsMap.set(id, team);
    
    // Add owner as team member with 'owner' role
    await this.addTeamMember({
      teamId: id,
      userId: team.ownerId,
      role: 'owner',
      status: 'active'
    });
    
    return team;
  }
  
  async updateTeam(id: number, teamUpdate: Partial<Team>): Promise<Team | undefined> {
    const team = this.teamsMap.get(id);
    if (!team) return undefined;
    
    const updatedTeam = { ...team, ...teamUpdate, updatedAt: new Date() };
    this.teamsMap.set(id, updatedTeam);
    
    return updatedTeam;
  }
  
  async deleteTeam(id: number): Promise<boolean> {
    const team = this.teamsMap.get(id);
    if (!team) return false;
    
    // Remove all team members
    this.teamMembersMap.delete(id);
    
    // Remove all team courses
    this.teamCoursesMap.delete(id);
    
    return this.teamsMap.delete(id);
  }
  
  // Team Members operations
  async getTeamMembers(teamId: number): Promise<TeamMember[]> {
    return this.teamMembersMap.get(teamId) || [];
  }
  
  async getTeamMembersByUserId(userId: number): Promise<TeamMember[]> {
    const result: TeamMember[] = [];
    
    this.teamMembersMap.forEach((members) => {
      const member = members.find(m => m.userId === userId);
      if (member) result.push(member);
    });
    
    return result;
  }
  
  async addTeamMember(teamMember: InsertTeamMember): Promise<TeamMember> {
    const { teamId } = teamMember;
    
    // Initialize the array if it doesn't exist
    if (!this.teamMembersMap.has(teamId)) {
      this.teamMembersMap.set(teamId, []);
    }
    
    const members = this.teamMembersMap.get(teamId) || [];
    
    // Check if user is already a member
    const existingMemberIndex = members.findIndex(m => m.userId === teamMember.userId);
    
    const member: TeamMember = {
      ...teamMember,
      role: teamMember.role || 'member',
      status: teamMember.status || 'active',
      joinedAt: new Date()
    };
    
    if (existingMemberIndex >= 0) {
      // Update the existing member
      members[existingMemberIndex] = member;
    } else {
      // Add new member
      members.push(member);
    }
    
    this.teamMembersMap.set(teamId, members);
    
    return member;
  }
  
  async updateTeamMemberRole(teamId: number, userId: number, role: string): Promise<TeamMember | undefined> {
    const members = this.teamMembersMap.get(teamId) || [];
    const memberIndex = members.findIndex(m => m.userId === userId);
    
    if (memberIndex < 0) return undefined;
    
    const updatedMember = { ...members[memberIndex], role };
    members[memberIndex] = updatedMember;
    
    this.teamMembersMap.set(teamId, members);
    
    return updatedMember;
  }
  
  async removeTeamMember(teamId: number, userId: number): Promise<boolean> {
    const members = this.teamMembersMap.get(teamId) || [];
    const newMembers = members.filter(m => m.userId !== userId);
    
    if (newMembers.length === members.length) return false;
    
    this.teamMembersMap.set(teamId, newMembers);
    
    return true;
  }
  
  // Course Collaborators operations
  async getCourseCollaborators(courseId: number): Promise<CourseCollaborator[]> {
    return this.courseCollaboratorsMap.get(courseId) || [];
  }
  
  async getUserCollaboratedCourses(userId: number): Promise<Course[]> {
    const collaboratedCourseIds: number[] = [];
    
    this.courseCollaboratorsMap.forEach((collaborators, courseId) => {
      if (collaborators.some(collab => collab.userId === userId)) {
        collaboratedCourseIds.push(courseId);
      }
    });
    
    return collaboratedCourseIds
      .map(id => this.courses.get(id))
      .filter((course): course is Course => !!course);
  }
  
  async addCourseCollaborator(collaborator: InsertCourseCollaborator): Promise<CourseCollaborator> {
    const { courseId } = collaborator;
    
    // Initialize the array if it doesn't exist
    if (!this.courseCollaboratorsMap.has(courseId)) {
      this.courseCollaboratorsMap.set(courseId, []);
    }
    
    const collaborators = this.courseCollaboratorsMap.get(courseId) || [];
    
    // Check if user is already a collaborator
    const existingCollabIndex = collaborators.findIndex(c => c.userId === collaborator.userId);
    
    const newCollaborator: CourseCollaborator = {
      ...collaborator,
      role: collaborator.role || 'editor',
      canEdit: collaborator.canEdit !== undefined ? collaborator.canEdit : true,
      addedAt: new Date()
    };
    
    if (existingCollabIndex >= 0) {
      // Update the existing collaborator
      collaborators[existingCollabIndex] = newCollaborator;
    } else {
      // Add new collaborator
      collaborators.push(newCollaborator);
    }
    
    this.courseCollaboratorsMap.set(courseId, collaborators);
    
    return newCollaborator;
  }
  
  async updateCourseCollaborator(courseId: number, userId: number, update: Partial<CourseCollaborator>): Promise<CourseCollaborator | undefined> {
    const collaborators = this.courseCollaboratorsMap.get(courseId) || [];
    const collabIndex = collaborators.findIndex(c => c.userId === userId);
    
    if (collabIndex < 0) return undefined;
    
    const updatedCollaborator = { ...collaborators[collabIndex], ...update };
    collaborators[collabIndex] = updatedCollaborator;
    
    this.courseCollaboratorsMap.set(courseId, collaborators);
    
    return updatedCollaborator;
  }
  
  async removeCourseCollaborator(courseId: number, userId: number): Promise<boolean> {
    const collaborators = this.courseCollaboratorsMap.get(courseId) || [];
    const newCollaborators = collaborators.filter(c => c.userId !== userId);
    
    if (newCollaborators.length === collaborators.length) return false;
    
    this.courseCollaboratorsMap.set(courseId, newCollaborators);
    
    return true;
  }
  
  // Team Courses operations
  async getTeamCourses(teamId: number): Promise<Course[]> {
    const teamCourses = this.teamCoursesMap.get(teamId) || [];
    
    return teamCourses
      .map(tc => this.courses.get(tc.courseId))
      .filter((course): course is Course => !!course);
  }
  
  async addCourseToTeam(teamCourse: InsertTeamCourse): Promise<TeamCourse> {
    const { teamId } = teamCourse;
    
    // Initialize the array if it doesn't exist
    if (!this.teamCoursesMap.has(teamId)) {
      this.teamCoursesMap.set(teamId, []);
    }
    
    const teamCourses = this.teamCoursesMap.get(teamId) || [];
    
    // Check if course is already in team
    const existingTeamCourseIndex = teamCourses.findIndex(tc => tc.courseId === teamCourse.courseId);
    
    const newTeamCourse: TeamCourse = {
      ...teamCourse,
      addedAt: new Date()
    };
    
    if (existingTeamCourseIndex >= 0) {
      // Update the existing team course
      teamCourses[existingTeamCourseIndex] = newTeamCourse;
    } else {
      // Add new team course
      teamCourses.push(newTeamCourse);
    }
    
    this.teamCoursesMap.set(teamId, teamCourses);
    
    return newTeamCourse;
  }
  
  async removeCourseFromTeam(teamId: number, courseId: number): Promise<boolean> {
    const teamCourses = this.teamCoursesMap.get(teamId) || [];
    const newTeamCourses = teamCourses.filter(tc => tc.courseId !== courseId);
    
    if (newTeamCourses.length === teamCourses.length) return false;
    
    this.teamCoursesMap.set(teamId, newTeamCourses);
    
    return true;
  }

  // Course Analytics operations
  async getCourseAnalytics(courseId: number): Promise<CourseAnalytics | undefined> {
    return Array.from(this.courseAnalyticsMap.values()).find(
      analytics => analytics.courseId === courseId
    );
  }
  
  async createCourseAnalytics(insertAnalytics: InsertCourseAnalytics): Promise<CourseAnalytics> {
    const id = this.currentCourseAnalyticsId++;
    const now = new Date();
    
    const analytics: CourseAnalytics = {
      ...insertAnalytics,
      id,
      totalViews: insertAnalytics.totalViews || 0,
      totalCompletions: insertAnalytics.totalCompletions || 0,
      averageRating: insertAnalytics.averageRating || "0",
      ratingsCount: insertAnalytics.ratingsCount || 0,
      studentsEnrolled: insertAnalytics.studentsEnrolled || 0,
      averageCompletionTime: insertAnalytics.averageCompletionTime || 0,
      createdAt: now,
      updatedAt: now
    };
    
    this.courseAnalyticsMap.set(id, analytics);
    return analytics;
  }
  
  async updateCourseAnalytics(courseId: number, analyticsUpdate: Partial<CourseAnalytics>): Promise<CourseAnalytics | undefined> {
    const analytics = await this.getCourseAnalytics(courseId);
    if (!analytics) return undefined;
    
    const updatedAnalytics = { ...analytics, ...analyticsUpdate, updatedAt: new Date() };
    this.courseAnalyticsMap.set(analytics.id, updatedAnalytics);
    
    return updatedAnalytics;
  }
  
  // Lesson Analytics operations
  async getLessonAnalytics(lessonId: number): Promise<LessonAnalytics | undefined> {
    return Array.from(this.lessonAnalyticsMap.values()).find(
      analytics => analytics.lessonId === lessonId
    );
  }
  
  async getLessonAnalyticsByCourseId(courseId: number): Promise<LessonAnalytics[]> {
    const lessons = await this.getLessonsByCourseId(courseId);
    const lessonIds = lessons.map(lesson => lesson.id);
    
    return Array.from(this.lessonAnalyticsMap.values()).filter(
      analytics => lessonIds.includes(analytics.lessonId)
    );
  }
  
  async createLessonAnalytics(insertAnalytics: InsertLessonAnalytics): Promise<LessonAnalytics> {
    const id = this.currentLessonAnalyticsId++;
    const now = new Date();
    
    const analytics: LessonAnalytics = {
      ...insertAnalytics,
      id,
      totalViews: insertAnalytics.totalViews || 0,
      completionRate: insertAnalytics.completionRate || "0",
      averageTimeSpent: insertAnalytics.averageTimeSpent || 0,
      engagementScore: insertAnalytics.engagementScore || "0",
      createdAt: now,
      updatedAt: now
    };
    
    this.lessonAnalyticsMap.set(id, analytics);
    return analytics;
  }
  
  async updateLessonAnalytics(lessonId: number, analyticsUpdate: Partial<LessonAnalytics>): Promise<LessonAnalytics | undefined> {
    const analytics = await this.getLessonAnalytics(lessonId);
    if (!analytics) return undefined;
    
    const updatedAnalytics = { ...analytics, ...analyticsUpdate, updatedAt: new Date() };
    this.lessonAnalyticsMap.set(analytics.id, updatedAnalytics);
    
    return updatedAnalytics;
  }
  
  // User Course Progress operations
  async getUserCourseProgress(userId: number, courseId: number): Promise<UserCourseProgress | undefined> {
    return this.userCourseProgressMap.get(`${userId}-${courseId}`);
  }
  
  async getUserCourseProgressByUserId(userId: number): Promise<UserCourseProgress[]> {
    return Array.from(this.userCourseProgressMap.values()).filter(
      progress => progress.userId === userId
    );
  }
  
  async getUsersByCourseId(courseId: number): Promise<UserCourseProgress[]> {
    return Array.from(this.userCourseProgressMap.values()).filter(
      progress => progress.courseId === courseId
    );
  }
  
  async createUserCourseProgress(insertProgress: InsertUserCourseProgress): Promise<UserCourseProgress> {
    const id = this.currentUserCourseProgressId++;
    const now = new Date();
    
    const progress: UserCourseProgress = {
      ...insertProgress,
      id,
      completionPercentage: insertProgress.completionPercentage || "0",
      isCompleted: insertProgress.isCompleted || false,
      timeSpent: insertProgress.timeSpent || 0,
      rating: insertProgress.rating || null,
      feedback: insertProgress.feedback || "",
      enrolledAt: insertProgress.enrolledAt || now,
      lastAccessedAt: insertProgress.lastAccessedAt || now
    };
    
    this.userCourseProgressMap.set(`${progress.userId}-${progress.courseId}`, progress);
    
    // Update course analytics if it exists
    const courseAnalytics = await this.getCourseAnalytics(progress.courseId);
    if (courseAnalytics) {
      await this.updateCourseAnalytics(progress.courseId, {
        studentsEnrolled: courseAnalytics.studentsEnrolled + 1
      });
    } else {
      // Create course analytics if it doesn't exist
      await this.createCourseAnalytics({
        courseId: progress.courseId,
        studentsEnrolled: 1
      });
    }
    
    return progress;
  }
  
  async updateUserCourseProgress(userId: number, courseId: number, progressUpdate: Partial<UserCourseProgress>): Promise<UserCourseProgress | undefined> {
    const progress = await this.getUserCourseProgress(userId, courseId);
    if (!progress) return undefined;
    
    const now = new Date();
    const updatedProgress = { 
      ...progress, 
      ...progressUpdate, 
      lastAccessedAt: progressUpdate.lastAccessedAt || now 
    };
    this.userCourseProgressMap.set(`${userId}-${courseId}`, updatedProgress);
    
    // If user completed the course, update course analytics
    if (progressUpdate.isCompleted && !progress.isCompleted) {
      const courseAnalytics = await this.getCourseAnalytics(courseId);
      if (courseAnalytics) {
        await this.updateCourseAnalytics(courseId, {
          totalCompletions: courseAnalytics.totalCompletions + 1
        });
      }
    }
    
    // If user rated the course, update course analytics
    if (progressUpdate.rating && progressUpdate.rating !== progress.rating) {
      const courseAnalytics = await this.getCourseAnalytics(courseId);
      if (courseAnalytics) {
        const newRatingsCount = courseAnalytics.ratingsCount + 1;
        const currentRating = courseAnalytics.averageRating ? parseFloat(courseAnalytics.averageRating) : 0;
        const newAverageRating = (
          (currentRating * courseAnalytics.ratingsCount + 
           progressUpdate.rating) / newRatingsCount
        ).toFixed(2);
        
        await this.updateCourseAnalytics(courseId, {
          ratingsCount: newRatingsCount,
          averageRating: newAverageRating
        });
      }
    }
    
    return updatedProgress;
  }
  
  // User Lesson Progress operations
  async getUserLessonProgress(userId: number, lessonId: number): Promise<UserLessonProgress | undefined> {
    return this.userLessonProgressMap.get(`${userId}-${lessonId}`);
  }
  
  async getUserLessonProgressByCourseId(userId: number, courseId: number): Promise<UserLessonProgress[]> {
    return Array.from(this.userLessonProgressMap.values()).filter(
      progress => progress.userId === userId && progress.courseId === courseId
    );
  }
  
  async createUserLessonProgress(insertProgress: InsertUserLessonProgress): Promise<UserLessonProgress> {
    const id = this.currentUserLessonProgressId++;
    const now = new Date();
    
    const progress: UserLessonProgress = {
      ...insertProgress,
      id,
      timeSpent: insertProgress.timeSpent || 0,
      isCompleted: insertProgress.isCompleted || false,
      completedAt: insertProgress.completedAt || null,
      videoPosition: insertProgress.videoPosition || 0,
      startedAt: insertProgress.startedAt || now,
      lastAccessedAt: insertProgress.lastAccessedAt || now
    };
    
    this.userLessonProgressMap.set(`${progress.userId}-${progress.lessonId}`, progress);
    
    // Update lesson analytics if it exists
    const lessonAnalytics = await this.getLessonAnalytics(progress.lessonId);
    if (lessonAnalytics) {
      await this.updateLessonAnalytics(progress.lessonId, {
        totalViews: lessonAnalytics.totalViews + 1
      });
    } else {
      // Create lesson analytics if it doesn't exist
      await this.createLessonAnalytics({
        lessonId: progress.lessonId,
        totalViews: 1
      });
    }
    
    return progress;
  }
  
  async updateUserLessonProgress(userId: number, lessonId: number, progressUpdate: Partial<UserLessonProgress>): Promise<UserLessonProgress | undefined> {
    const progress = await this.getUserLessonProgress(userId, lessonId);
    if (!progress) return undefined;
    
    const now = new Date();
    const updatedProgress = { 
      ...progress, 
      ...progressUpdate, 
      lastAccessedAt: progressUpdate.lastAccessedAt || now 
    };
    
    // If completing a lesson, set completedAt if not already set
    if (progressUpdate.isCompleted && !progress.isCompleted) {
      updatedProgress.completedAt = now;
      
      // Update lesson analytics
      const lessonAnalytics = await this.getLessonAnalytics(lessonId);
      if (lessonAnalytics) {
        // Recalculate completion rate
        const allLessonProgress = Array.from(this.userLessonProgressMap.values()).filter(
          p => p.lessonId === lessonId
        );
        const completedCount = allLessonProgress.filter(p => p.isCompleted).length;
        const completionRate = (completedCount / allLessonProgress.length * 100).toFixed(2);
        
        await this.updateLessonAnalytics(lessonId, {
          completionRate
        });
      }
      
      // Update user course progress
      const courseProgress = await this.getUserCourseProgress(userId, progress.courseId);
      if (courseProgress) {
        // Calculate new completion percentage for the course
        const courseLessons = await this.getLessonsByCourseId(progress.courseId);
        const userLessonProgress = await this.getUserLessonProgressByCourseId(userId, progress.courseId);
        const completedLessons = userLessonProgress.filter(p => p.isCompleted).length;
        const newCompletionPercentage = ((completedLessons + 1) / courseLessons.length * 100).toFixed(2);
        
        // Check if all lessons are completed to mark course as completed
        const isAllCompleted = completedLessons + 1 >= courseLessons.length;
        
        await this.updateUserCourseProgress(userId, progress.courseId, {
          completionPercentage: newCompletionPercentage,
          isCompleted: isAllCompleted
        });
      }
    }
    
    this.userLessonProgressMap.set(`${userId}-${lessonId}`, updatedProgress);
    return updatedProgress;
  }
  
  // Analytics Events operations
  async recordAnalyticsEvent(insertEvent: InsertAnalyticsEvent): Promise<AnalyticsEvent> {
    const id = this.currentAnalyticsEventId++;
    const now = new Date();
    
    const event: AnalyticsEvent = {
      id,
      userId: insertEvent.userId || null,
      courseId: insertEvent.courseId || null,
      lessonId: insertEvent.lessonId || null,
      eventType: insertEvent.eventType,
      eventData: insertEvent.eventData || null,
      ipAddress: insertEvent.ipAddress || null,
      userAgent: insertEvent.userAgent || null,
      createdAt: now
    };
    
    this.analyticsEventsArray.push(event);
    
    // Update relevant analytics based on event type
    if (event.courseId) {
      if (event.eventType === "view") {
        const courseAnalytics = await this.getCourseAnalytics(event.courseId);
        if (courseAnalytics) {
          await this.updateCourseAnalytics(event.courseId, {
            totalViews: courseAnalytics.totalViews + 1
          });
        } else {
          await this.createCourseAnalytics({
            courseId: event.courseId,
            totalViews: 1
          });
        }
      }
    }
    
    if (event.lessonId) {
      if (event.eventType === "view") {
        const lessonAnalytics = await this.getLessonAnalytics(event.lessonId);
        if (lessonAnalytics) {
          await this.updateLessonAnalytics(event.lessonId, {
            totalViews: lessonAnalytics.totalViews + 1
          });
        } else {
          await this.createLessonAnalytics({
            lessonId: event.lessonId,
            totalViews: 1
          });
        }
      }
    }
    
    return event;
  }
  
  async getAnalyticsEventsByCourseId(courseId: number, limit?: number): Promise<AnalyticsEvent[]> {
    const events = this.analyticsEventsArray
      .filter(event => event.courseId === courseId)
      .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
    
    if (limit) {
      return events.slice(0, limit);
    }
    
    return events;
  }
  
  async getAnalyticsEventsByUserId(userId: number, limit?: number): Promise<AnalyticsEvent[]> {
    const events = this.analyticsEventsArray
      .filter(event => event.userId === userId)
      .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
    
    if (limit) {
      return events.slice(0, limit);
    }
    
    return events;
  }
  
  async getAnalyticsEventsByLessonId(lessonId: number, limit?: number): Promise<AnalyticsEvent[]> {
    const events = this.analyticsEventsArray
      .filter(event => event.lessonId === lessonId)
      .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
    
    if (limit) {
      return events.slice(0, limit);
    }
    
    return events;
  }
  
  // Publishing operations
  async getPublishingByCourseId(courseId: number): Promise<Publishing[]> {
    return Array.from(this.publishingMap.values()).filter(
      publishing => publishing.courseId === courseId
    );
  }
  
  async getPublishingByPlatform(platform: string): Promise<Publishing[]> {
    return Array.from(this.publishingMap.values()).filter(
      publishing => publishing.platform === platform
    );
  }
  
  async createPublishing(insertPublishing: InsertPublishing): Promise<Publishing> {
    const id = this.currentPublishingId++;
    const now = new Date();
    
    const publishing: Publishing = {
      ...insertPublishing,
      id,
      status: insertPublishing.status || 'pending',
      platformUrl: insertPublishing.platformUrl || null,
      analyticsData: insertPublishing.analyticsData || {},
      publishedAt: insertPublishing.publishedAt || now,
      createdAt: now,
      updatedAt: now
    };
    
    this.publishingMap.set(id, publishing);
    return publishing;
  }
  
  async updatePublishing(id: number, publishingUpdate: Partial<Publishing>): Promise<Publishing | undefined> {
    const publishing = this.publishingMap.get(id);
    if (!publishing) return undefined;
    
    const now = new Date();
    const updatedPublishing = {
      ...publishing,
      ...publishingUpdate,
      updatedAt: now
    };
    
    this.publishingMap.set(id, updatedPublishing);
    return updatedPublishing;
  }
  
  async deletePublishing(id: number): Promise<boolean> {
    return this.publishingMap.delete(id);
  }
  
  // Dashboard Analytics
  async getCoursePerformanceOverview(courseId: number): Promise<any> {
    const courseAnalytics = await this.getCourseAnalytics(courseId);
    const lessonAnalytics = await this.getLessonAnalyticsByCourseId(courseId);
    const userProgress = await this.getUsersByCourseId(courseId);
    
    // Calculate completion rates
    const completedUsers = userProgress.filter(progress => progress.isCompleted);
    const completionRate = userProgress.length > 0 
      ? (completedUsers.length / userProgress.length * 100).toFixed(2)
      : "0";
    
    // Calculate average time spent
    const totalTimeSpent = userProgress.reduce((sum, progress) => sum + progress.timeSpent, 0);
    const averageTimeSpent = userProgress.length > 0 
      ? Math.round(totalTimeSpent / userProgress.length) 
      : 0;
    
    // Calculate engagement score (0-100)
    const engagementScore = userProgress.length > 0
      ? (userProgress.reduce((sum, progress) => {
          const completionPercentage = progress.completionPercentage || "0";
          const progressScore = parseFloat(completionPercentage) / 100 * 0.6; // 60% weight to completion
          const timeScore = Math.min(progress.timeSpent / 3600, 1) * 0.4; // 40% weight to time spent (max 1h)
          return sum + progressScore + timeScore;
        }, 0) / userProgress.length * 100).toFixed(2)
      : "0";
    
    // Get popular lessons based on views
    const lessonPopularity = lessonAnalytics.sort((a, b) => b.totalViews - a.totalViews);
    
    return {
      id: courseId,
      totalViews: courseAnalytics?.totalViews || 0,
      totalCompletions: courseAnalytics?.totalCompletions || 0,
      totalEnrollments: courseAnalytics?.studentsEnrolled || 0,
      averageRating: courseAnalytics?.averageRating || "0",
      ratingsCount: courseAnalytics?.ratingsCount || 0,
      completionRate,
      averageTimeSpent,
      engagementScore,
      popularLessons: lessonPopularity.slice(0, 5).map(a => ({
        lessonId: a.lessonId,
        views: a.totalViews,
        completionRate: a.completionRate
      }))
    };
  }
  
  async getUserEngagementStats(userId: number): Promise<any> {
    const userCourseProgress = await this.getUserCourseProgressByUserId(userId);
    
    // Calculate overall completion
    const enrolledCourses = userCourseProgress.length;
    const completedCourses = userCourseProgress.filter(progress => progress.isCompleted).length;
    const overallCompletion = enrolledCourses > 0 
      ? (completedCourses / enrolledCourses * 100).toFixed(2)
      : "0";
    
    // Calculate total time spent
    const totalTimeSpent = userCourseProgress.reduce((sum, progress) => sum + progress.timeSpent, 0);
    
    // Calculate average rating given
    const ratedCourses = userCourseProgress.filter(progress => progress.rating !== null);
    const averageRating = ratedCourses.length > 0
      ? (ratedCourses.reduce((sum, progress) => sum + (progress.rating || 0), 0) / ratedCourses.length).toFixed(1)
      : "0";
    
    // Get recent activities
    const recentActivities = await this.getAnalyticsEventsByUserId(userId, 10);
    
    return {
      userId,
      enrolledCourses,
      completedCourses,
      overallCompletion,
      totalTimeSpent,
      averageRating,
      recentActivities: recentActivities.map(event => ({
        eventType: event.eventType,
        courseId: event.courseId,
        lessonId: event.lessonId,
        createdAt: event.createdAt
      }))
    };
  }
  
  async getPopularCourses(limit: number = 5): Promise<any[]> {
    const courseIds = Array.from(this.courses.values()).map(course => course.id);
    const courseAnalytics = await Promise.all(
      courseIds.map(async id => {
        const analytics = await this.getCourseAnalytics(id);
        const course = await this.getCourse(id);
        return {
          id,
          title: course?.title || "",
          views: analytics?.totalViews || 0,
          students: analytics?.studentsEnrolled || 0,
          rating: analytics?.averageRating || "0",
          completions: analytics?.totalCompletions || 0
        };
      })
    );
    
    // Sort by popularity (combination of views, students, and rating)
    return courseAnalytics
      .sort((a, b) => {
        const aScore = a.views * 0.4 + a.students * 0.4 + parseFloat(a.rating) * 20;
        const bScore = b.views * 0.4 + b.students * 0.4 + parseFloat(b.rating) * 20;
        return bScore - aScore;
      })
      .slice(0, limit);
  }

  // AI Generated Images operations
  async getAiGeneratedImage(id: number): Promise<AiGeneratedImage | undefined> {
    return this.aiGeneratedImagesMap.get(id);
  }

  async getAiGeneratedImagesByUserId(userId: number): Promise<AiGeneratedImage[]> {
    return Array.from(this.aiGeneratedImagesMap.values()).filter(
      image => image.userId === userId
    );
  }

  async createAiGeneratedImage(insertImage: InsertAiGeneratedImage): Promise<AiGeneratedImage> {
    const id = this.currentAiGeneratedImageId++;
    const now = new Date();

    // Create a complete AI generated image object with all required fields
    const aiGeneratedImage: AiGeneratedImage = {
      ...insertImage,
      id,
      createdAt: now,
      courseId: insertImage.courseId || null,
      lessonId: insertImage.lessonId || null,
      mediaId: insertImage.mediaId || null,
      engineId: insertImage.engineId || null,
      seed: insertImage.seed || null,
      stylePreset: insertImage.stylePreset || null,
      negativePrompt: insertImage.negativePrompt || null
    };

    this.aiGeneratedImagesMap.set(id, aiGeneratedImage);

    // Update user AI usage stats
    const stats = await this.getUserStats(insertImage.userId);
    if (stats) {
      // Deduct 5 AI credits per image generation
      const aiCreditsToDeduct = 5;
      await this.updateUserStats(insertImage.userId, {
        aiCredits: Math.max(0, (stats.aiCredits || 0) - aiCreditsToDeduct)
      });
    }

    return aiGeneratedImage;
  }

  async deleteAiGeneratedImage(id: number): Promise<boolean> {
    const image = this.aiGeneratedImagesMap.get(id);
    if (!image) return false;

    // If the image has an associated media entry, delete that too
    if (image.mediaId) {
      await this.deleteMedia(image.mediaId);
    }

    return this.aiGeneratedImagesMap.delete(id);
  }

  // Generated Videos operations
  async getGeneratedVideo(id: string): Promise<GeneratedVideo | undefined> {
    return this.generatedVideosMap.get(id);
  }

  async getGeneratedVideosByUserId(userId: number): Promise<GeneratedVideo[]> {
    return Array.from(this.generatedVideosMap.values()).filter(
      video => video.userId === userId
    );
  }

  async createGeneratedVideo(insertVideo: InsertGeneratedVideo): Promise<GeneratedVideo> {
    const now = new Date();

    // Create a complete generated video object with all required fields
    const generatedVideo: GeneratedVideo = {
      ...insertVideo,
      createdAt: now,
      courseId: insertVideo.courseId || null,
      lessonId: insertVideo.lessonId || null,
      mediaId: insertVideo.mediaId || null,
      videoUrl: insertVideo.videoUrl || null,
      thumbnailUrl: insertVideo.thumbnailUrl || null,
      error: insertVideo.error || null,
      duration: insertVideo.duration || null,
      processingStatus: insertVideo.processingStatus || 'pending',
      animationKeyframes: insertVideo.animationKeyframes || null
    };

    this.generatedVideosMap.set(insertVideo.id, generatedVideo);

    // If this is an AI-generated video (not a free import), update user's AI credits
    if (!insertVideo.id.startsWith('free-')) {
      const stats = await this.getUserStats(insertVideo.userId);
      if (stats) {
        // Deduct 200 AI credits for AI video generation
        const aiCreditsToDeduct = 200;
        await this.updateUserStats(insertVideo.userId, {
          aiCredits: Math.max(0, (stats.aiCredits || 0) - aiCreditsToDeduct)
        });
      }
    }

    return generatedVideo;
  }

  async updateGeneratedVideo(id: string, videoUpdate: Partial<GeneratedVideo>): Promise<GeneratedVideo | undefined> {
    const existingVideo = this.generatedVideosMap.get(id);
    if (!existingVideo) return undefined;

    const updatedVideo: GeneratedVideo = {
      ...existingVideo,
      ...videoUpdate
    };

    this.generatedVideosMap.set(id, updatedVideo);
    return updatedVideo;
  }

  async deleteGeneratedVideo(id: string): Promise<boolean> {
    const video = this.generatedVideosMap.get(id);
    if (!video) return false;

    // If the video has an associated media entry, delete that too
    if (video.mediaId) {
      await this.deleteMedia(video.mediaId);
    }

    return this.generatedVideosMap.delete(id);
  }

  // Mini Courses operations
  async getMiniCourse(id: number): Promise<MiniCourse | undefined> {
    return this.miniCoursesMap.get(id);
  }

  async getMiniCoursesByUserId(userId: number): Promise<MiniCourse[]> {
    const result: MiniCourse[] = [];
    for (const miniCourse of this.miniCoursesMap.values()) {
      if (miniCourse.userId === userId) {
        result.push(miniCourse);
      }
    }
    return result;
  }

  async createMiniCourse(miniCourse: InsertMiniCourse): Promise<MiniCourse> {
    const id = this.currentMiniCourseId++;
    const now = new Date();
    
    const newMiniCourse: MiniCourse = {
      id,
      ...miniCourse,
      status: miniCourse.status || 'draft',
      createdAt: now,
      updatedAt: now,
      publishedAt: miniCourse.status === 'published' ? now : null,
      scriptId: miniCourse.scriptId || null,
      audioId: miniCourse.audioId || null,
      videoId: miniCourse.videoId || null,
      quizId: miniCourse.quizId || null,
      thumbnailUrl: miniCourse.thumbnailUrl || null,
      contentSections: miniCourse.contentSections || null,
      targetAudience: miniCourse.targetAudience || null,
      difficultyLevel: miniCourse.difficultyLevel || 'beginner',
      includesResources: miniCourse.includesResources || false
    };
    
    this.miniCoursesMap.set(id, newMiniCourse);
    return newMiniCourse;
  }

  async updateMiniCourse(id: number, update: Partial<MiniCourse>): Promise<MiniCourse | undefined> {
    const miniCourse = this.miniCoursesMap.get(id);
    
    if (!miniCourse) {
      return undefined;
    }
    
    // Update mini course with new values
    const updatedMiniCourse: MiniCourse = {
      ...miniCourse,
      ...update,
      updatedAt: new Date()
    };
    
    // If status changed to published and it wasn't published before, set publishedAt
    if (update.status === 'published' && miniCourse.status !== 'published') {
      updatedMiniCourse.publishedAt = new Date();
    }
    
    this.miniCoursesMap.set(id, updatedMiniCourse);
    return updatedMiniCourse;
  }

  async deleteMiniCourse(id: number): Promise<boolean> {
    const miniCourse = this.miniCoursesMap.get(id);
    if (!miniCourse) return false;
    
    // Clean up related resources
    if (miniCourse.scriptId) {
      await this.deleteMedia(miniCourse.scriptId);
    }
    
    if (miniCourse.audioId) {
      await this.deleteMedia(miniCourse.audioId);
    }
    
    if (miniCourse.videoId) {
      await this.deleteMedia(miniCourse.videoId);
    }
    
    return this.miniCoursesMap.delete(id);
  }
  
  // Quiz operations
  async deleteQuiz(id: number): Promise<boolean> {
    // Check if quiz exists
    if (!this.quizzesMap.has(id)) {
      return false;
    }
    
    // Delete all associated quiz questions, answers, and flashcards
    if (this.quizQuestionsMap.has(id)) {
      const questionIds = this.quizQuestionsMap.get(id)?.map(q => q.id) || [];
      
      // Delete answers for each question
      for (const questionId of questionIds) {
        this.quizAnswersMap.delete(questionId);
      }
      
      // Delete questions
      this.quizQuestionsMap.delete(id);
    }
    
    // Delete flashcards
    this.quizFlashcardsMap.delete(id);
    
    // Delete the quiz itself
    this.quizzesMap.delete(id);
    
    return true;
  }
  
  // AI Credits operations
  async deductUserCredits(userId: number, creditsToDeduct: number): Promise<boolean> {
    try {
      const stats = await this.getUserStats(userId);
      
      if (!stats || stats.aiCredits === null || stats.aiCredits < creditsToDeduct) {
        return false; // Not enough credits
      }
      
      // Update the user's credits
      await this.updateUserStats(userId, {
        aiCredits: Math.max(0, stats.aiCredits - creditsToDeduct)
      });
      
      return true;
    } catch (err) {
      console.error("Error deducting user credits:", err);
      return false;
    }
  }

  // Platform integration operations
  async getAllPlatforms(userId?: number): Promise<any[]> {
    // Default platforms data
    const platforms = [
      {
        id: 1,
        name: "Udemy",
        slug: "udemy",
        icon: "SiUdemy",
        description: "Publish your course to the Udemy marketplace",
        category: "marketplace",
        authType: "apiKey",
        fields: [
          {
            name: "apiKey",
            label: "API Key",
            type: "password",
            required: true
          },
          {
            name: "domain",
            label: "Domain",
            type: "text",
            required: false
          }
        ]
      },
      {
        id: 2,
        name: "Teachable",
        slug: "teachable",
        icon: "SiTeachable",
        description: "Publish to your Teachable school",
        category: "hosted_platform",
        authType: "apiKey",
        fields: [
          {
            name: "apiKey",
            label: "API Key",
            type: "password",
            required: true
          },
          {
            name: "domain",
            label: "School Subdomain",
            type: "text",
            required: true,
            placeholder: "your-school.teachable.com"
          }
        ]
      },
      {
        id: 3,
        name: "Thinkific",
        slug: "thinkific",
        icon: "SiThinkific",
        description: "Publish your course to your Thinkific site",
        category: "hosted_platform",
        authType: "oauth",
        oauthUrl: "/api/auth/oauth/thinkific"
      },
      {
        id: 4,
        name: "YouTube",
        slug: "youtube",
        icon: "SiYoutube",
        description: "Publish your video lessons to YouTube",
        category: "video_platform",
        authType: "oauth",
        oauthUrl: "/api/auth/oauth/youtube"
      },
      {
        id: 5,
        name: "Vimeo",
        slug: "vimeo",
        icon: "SiVimeo",
        description: "Host your course videos on Vimeo",
        category: "video_platform",
        authType: "apiKey",
        fields: [
          {
            name: "accessToken",
            label: "Access Token",
            type: "password",
            required: true
          }
        ]
      },
      {
        id: 6,
        name: "Kajabi",
        slug: "kajabi",
        icon: "SiKajabi",
        description: "Publish your course to your Kajabi site",
        category: "hosted_platform",
        authType: "apiKey",
        fields: [
          {
            name: "apiKey",
            label: "API Key",
            type: "password",
            required: true
          },
          {
            name: "domain",
            label: "Site Domain",
            type: "text",
            required: true,
            placeholder: "your-site.mykajabi.com"
          }
        ]
      },
      // Slack platform has been removed
    ];

    // If userId is provided, include connection status
    if (userId) {
      return platforms.map(platform => {
        const connectionKey = `${userId}-${platform.slug}`;
        const connection = this.platformConnectionsMap.get(connectionKey);
        
        return {
          ...platform,
          connected: !!connection,
          connectionId: connection?.id,
          lastSyncedAt: connection?.lastSyncedAt
        };
      });
    }
    
    return platforms;
  }

  async getPlatformBySlug(slug: string): Promise<any | undefined> {
    const platforms = await this.getAllPlatforms();
    return platforms.find(p => p.slug === slug);
  }

  async connectPlatform(userId: number, platformSlug: string): Promise<boolean> {
    const platform = await this.getPlatformBySlug(platformSlug);
    if (!platform) return false;
    
    const connectionData = {
      id: Date.now(),
      userId,
      platformSlug,
      credentials: {},
      settings: {},
      lastSyncedAt: new Date().toISOString()
    };
    
    this.platformConnectionsMap.set(`${userId}-${platformSlug}`, connectionData);
    return true;
  }

  async disconnectPlatform(userId: number, platformId: number): Promise<boolean> {
    for (const [key, connection] of this.platformConnectionsMap.entries()) {
      if (connection.userId === userId && connection.id === platformId) {
        return this.platformConnectionsMap.delete(key);
      }
    }
    return false;
  }

  async isPlatformConnected(userId: number, platformSlug: string): Promise<boolean> {
    return !!this.platformConnectionsMap.get(`${userId}-${platformSlug}`);
  }

  async publishCourseToPlatform(userId: number, courseId: number, platformSlug: string, options?: any): Promise<any> {
    const course = await this.getCourse(courseId);
    if (!course || course.userId !== userId) return null;
    
    const isConnected = await this.isPlatformConnected(userId, platformSlug);
    if (!isConnected) return null;
    
    // In a real implementation, this would interface with the actual platform APIs
    const publicationData = {
      id: Date.now(),
      courseId,
      platformSlug,
      status: "published",
      publishedAt: new Date().toISOString(),
      platformUrl: `https://example.com/${platformSlug}/course/${courseId}`,
      analyticsData: {
        views: 0,
        enrollments: 0,
        rating: 0
      }
    };
    
    const key = `${userId}-${courseId}`;
    let publications = this.coursePublicationsMap.get(key) || [];
    publications.push(publicationData);
    this.coursePublicationsMap.set(key, publications);
    
    return publicationData;
  }

  async getCoursePublications(userId: number, courseId: number): Promise<any[]> {
    const course = await this.getCourse(courseId);
    if (!course || course.userId !== userId) return [];
    
    return this.coursePublicationsMap.get(`${userId}-${courseId}`) || [];
  }

  async isUserCourseCollaborator(userId: number, courseId: number): Promise<boolean> {
    const collaborators = await this.getCourseCollaborators(courseId);
    return collaborators.some(c => c.userId === userId);
  }

  // Platform integration methods
  private currentPlatformConnectionId: number = 1;

  async createPlatformConnection(data: any): Promise<any> {
    const id = this.currentPlatformConnectionId++;
    const connection = {
      id,
      ...data,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    
    const key = `${data.userId}-${data.platformSlug}`;
    this.platformConnectionsMap.set(key, connection);
    
    return connection;
  }

  async getPlatformConnection(id: number): Promise<any> {
    for (const connection of this.platformConnectionsMap.values()) {
      if (connection.id === id) {
        return connection;
      }
    }
    return undefined;
  }

  async getPlatformConnectionByUserAndPlatform(userId: number, platformSlug: string): Promise<any> {
    return this.platformConnectionsMap.get(`${userId}-${platformSlug}`);
  }

  async updatePlatformConnectionLastSynced(id: number): Promise<any> {
    const connection = await this.getPlatformConnection(id);
    if (!connection) return undefined;
    
    connection.lastSyncedAt = new Date().toISOString();
    connection.updatedAt = new Date().toISOString();
    
    // Update in the map
    const key = `${connection.userId}-${connection.platformSlug}`;
    this.platformConnectionsMap.set(key, connection);
    
    return connection;
  }

  async deletePlatformConnection(id: number): Promise<boolean> {
    const connection = await this.getPlatformConnection(id);
    if (!connection) return false;
    
    const key = `${connection.userId}-${connection.platformSlug}`;
    return this.platformConnectionsMap.delete(key);
  }
  
  // TTS (Text-to-Speech) methods
  async saveTTSRecord(record: TTSResponse): Promise<TTSRecord> {
    const newRecord: TTSRecord = {
      id: this.currentTTSRecordId++,
      userId: record.userId,
      text: record.text,
      fileName: record.fileName,
      filePath: record.filePath,
      service: record.service,
      wordCount: record.wordCount,
      creditCost: record.creditCost,
      title: record.title || null,
      metadata: record.metadata || {},
      createdAt: new Date()
    };
    
    this.ttsRecordsMap.set(newRecord.id, newRecord);
    return newRecord;
  }
  
  async getTTSHistory(userId: number): Promise<TTSRecord[]> {
    const records: TTSRecord[] = [];
    
    for (const record of this.ttsRecordsMap.values()) {
      if (record.userId === userId) {
        records.push(record);
      }
    }
    
    // Sort by creation date, newest first
    return records.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
  }
  
  async getTTSById(id: number): Promise<TTSRecord | undefined> {
    return this.ttsRecordsMap.get(id);
  }
  
  async deleteTTSRecord(id: number): Promise<boolean> {
    return this.ttsRecordsMap.delete(id);
  }
  
  // Landing Page operations
  async getLandingPage(id: number): Promise<LandingPage | undefined> {
    return this.landingPagesMap.get(id);
  }
  
  async getLandingPageBySlug(slug: string): Promise<LandingPage | undefined> {
    return Array.from(this.landingPagesMap.values()).find(page => page.slug === slug);
  }
  
  async getLandingPagesByCourseId(courseId: number): Promise<LandingPage[]> {
    return Array.from(this.landingPagesMap.values())
      .filter(page => page.courseId === courseId)
      .sort((a, b) => {
        if (!a.createdAt || !b.createdAt) return 0;
        return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
      });
  }
  
  async getLandingPagesByUserId(userId: number): Promise<LandingPage[]> {
    return Array.from(this.landingPagesMap.values())
      .filter(page => page.userId === userId)
      .sort((a, b) => {
        if (!a.createdAt || !b.createdAt) return 0;
        return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
      });
  }
  
  async createLandingPage(landingPage: InsertLandingPage): Promise<LandingPage> {
    const id = this.currentLandingPageId++;
    const now = new Date();
    
    const newLandingPage: LandingPage = {
      id,
      ...landingPage,
      viewCount: 0,
      uniqueVisitorCount: 0,
      conversionCount: 0,
      conversionRate: "0",
      status: "draft", 
      publishedAt: null,
      createdAt: now,
      updatedAt: now
    };
    
    this.landingPagesMap.set(id, newLandingPage);
    this.landingPageVisitsMap.set(id, []);
    
    return newLandingPage;
  }
  
  async updateLandingPage(id: number, landingPage: Partial<LandingPage>): Promise<LandingPage | undefined> {
    const existingPage = this.landingPagesMap.get(id);
    
    if (!existingPage) {
      return undefined;
    }
    
    const updatedPage: LandingPage = {
      ...existingPage,
      ...landingPage,
      updatedAt: new Date()
    };
    
    this.landingPagesMap.set(id, updatedPage);
    
    return updatedPage;
  }
  
  async publishLandingPage(id: number): Promise<LandingPage | undefined> {
    const existingPage = this.landingPagesMap.get(id);
    
    if (!existingPage) {
      return undefined;
    }
    
    const now = new Date();
    const publishedPage: LandingPage = {
      ...existingPage,
      status: "published",
      publishedAt: now,
      updatedAt: now
    };
    
    this.landingPagesMap.set(id, publishedPage);
    
    return publishedPage;
  }
  
  async deleteLandingPage(id: number): Promise<boolean> {
    const deleted = this.landingPagesMap.delete(id);
    this.landingPageVisitsMap.delete(id);
    return deleted;
  }
  
  // Landing Page Visit operations
  async recordLandingPageVisit(visit: InsertLandingPageVisit): Promise<LandingPageVisit> {
    const id = this.currentLandingPageVisitId++;
    const now = new Date();
    
    const newVisit: LandingPageVisit = {
      id,
      ...visit,
      converted: false,
      convertedAt: null,
      createdAt: now
    };
    
    const landingPageId = newVisit.landingPageId;
    const visits = this.landingPageVisitsMap.get(landingPageId) || [];
    visits.push(newVisit);
    this.landingPageVisitsMap.set(landingPageId, visits);
    
    // Update landing page stats
    await this.updateLandingPageStats(landingPageId);
    
    return newVisit;
  }
  
  async getLandingPageVisits(landingPageId: number): Promise<LandingPageVisit[]> {
    return this.landingPageVisitsMap.get(landingPageId) || [];
  }
  
  async recordLandingPageConversion(id: number): Promise<LandingPageVisit | undefined> {
    // Find the visit by ID
    for (const [landingPageId, visits] of this.landingPageVisitsMap.entries()) {
      const visitIndex = visits.findIndex(v => v.id === id);
      
      if (visitIndex !== -1) {
        const now = new Date();
        const updatedVisit: LandingPageVisit = {
          ...visits[visitIndex],
          converted: true,
          convertedAt: now
        };
        
        // Update the visit in the array
        visits[visitIndex] = updatedVisit;
        this.landingPageVisitsMap.set(landingPageId, visits);
        
        // Update landing page stats
        await this.updateLandingPageStats(landingPageId);
        
        return updatedVisit;
      }
    }
    
    return undefined;
  }
  
  async updateLandingPageStats(landingPageId: number): Promise<LandingPage | undefined> {
    const landingPage = this.landingPagesMap.get(landingPageId);
    const visits = this.landingPageVisitsMap.get(landingPageId) || [];
    
    if (!landingPage) {
      return undefined;
    }
    
    const totalVisits = visits.length;
    const totalConversions = visits.filter(v => v.converted).length;
    const conversionRate = totalVisits > 0 ? (totalConversions / totalVisits) * 100 : 0;
    
    // Update landing page stats
    const updatedPage: LandingPage = {
      ...landingPage,
      viewCount: totalVisits,
      uniqueVisitorCount: totalVisits, // Simplification, in reality we'd count unique IPs
      conversionCount: totalConversions,
      conversionRate: Math.round(conversionRate * 100) / 100 + "", // Convert to string and round to 2 decimal places
      updatedAt: new Date()
    };
    
    this.landingPagesMap.set(landingPageId, updatedPage);
    
    return updatedPage;
  }

  // Missing methods for IStorage compliance - stub implementations
  async getMicroLearningSegment(id: number): Promise<MicroLearningSegment | undefined> {
    return undefined;
  }
  async getMicroLearningSegmentsByLessonId(lessonId: number): Promise<MicroLearningSegment[]> {
    return [];
  }
  async createMicroLearningSegment(segment: InsertMicroLearningSegment): Promise<MicroLearningSegment> {
    return { id: Date.now(), ...segment } as MicroLearningSegment;
  }
  async updateMicroLearningSegment(id: number, segment: Partial<MicroLearningSegment>): Promise<MicroLearningSegment | undefined> {
    return undefined;
  }
  async deleteMicroLearningSegment(id: number): Promise<boolean> {
    return false;
  }
  async getMicroLearningKnowledgeCheck(id: number): Promise<MicroLearningKnowledgeCheck | undefined> {
    return undefined;
  }
  async getMicroLearningKnowledgeChecksByLessonId(lessonId: number): Promise<MicroLearningKnowledgeCheck[]> {
    return [];
  }
  async createMicroLearningKnowledgeCheck(check: InsertMicroLearningKnowledgeCheck): Promise<MicroLearningKnowledgeCheck> {
    return { id: Date.now(), ...check } as MicroLearningKnowledgeCheck;
  }
  async updateMicroLearningKnowledgeCheck(id: number, check: Partial<MicroLearningKnowledgeCheck>): Promise<MicroLearningKnowledgeCheck | undefined> {
    return undefined;
  }
  async deleteMicroLearningKnowledgeCheck(id: number): Promise<boolean> {
    return false;
  }
  async getMicroLearningUserProgress(id: number): Promise<MicroLearningUserProgress | undefined> {
    return undefined;
  }
  async getMicroLearningUserProgressByUserAndLesson(userId: number, lessonId: number): Promise<MicroLearningUserProgress | undefined> {
    return undefined;
  }
  async createMicroLearningUserProgress(progress: InsertMicroLearningUserProgress): Promise<MicroLearningUserProgress> {
    return { id: Date.now(), ...progress } as MicroLearningUserProgress;
  }
  async updateMicroLearningUserProgress(id: number, progress: Partial<MicroLearningUserProgress>): Promise<MicroLearningUserProgress | undefined> {
    return undefined;
  }
  async getUserKnowledgeCheckResponse(id: number): Promise<UserKnowledgeCheckResponse | undefined> {
    return undefined;
  }
  async getUserKnowledgeCheckResponsesByLessonId(userId: number, lessonId: number): Promise<UserKnowledgeCheckResponse[]> {
    return [];
  }
  async createUserKnowledgeCheckResponse(response: InsertUserKnowledgeCheckResponse): Promise<UserKnowledgeCheckResponse> {
    return { id: Date.now(), ...response } as UserKnowledgeCheckResponse;
  }
  async getAllTemplates(): Promise<Template[]> {
    return [];
  }
  async getTemplate(id: number): Promise<Template | undefined> {
    return undefined;
  }
  async getTemplateHistory(id: number): Promise<TemplateHistory | undefined> {
    return undefined;
  }
  async getTemplateHistoryByUserId(userId: number): Promise<TemplateHistory[]> {
    return [];
  }
  async createTemplateHistory(history: InsertTemplateHistory): Promise<TemplateHistory> {
    return { id: Date.now(), ...history } as TemplateHistory;
  }
  async updateTemplateHistoryFavorite(id: number, favorited: boolean): Promise<TemplateHistory | undefined> {
    return undefined;
  }
  async recordAnalyticsEvent(event: InsertAnalyticsEvent): Promise<AnalyticsEvent> {
    return { id: Date.now(), ...event } as AnalyticsEvent;
  }
  async getAnalyticsEventsByCourseId(courseId: number, limit?: number): Promise<AnalyticsEvent[]> {
    return [];
  }
  async getAnalyticsEventsByUserId(userId: number, limit?: number): Promise<AnalyticsEvent[]> {
    return [];
  }
  async getAnalyticsEventsByLessonId(lessonId: number, limit?: number): Promise<AnalyticsEvent[]> {
    return [];
  }
  async getCoursePerformanceOverview(courseId: number): Promise<any> {
    return {};
  }
  async getUserEngagementStats(userId: number): Promise<any> {
    return {};
  }
  async getPopularCourses(limit?: number): Promise<any[]> {
    return [];
  }
  async getAiGeneratedImage(id: number): Promise<AiGeneratedImage | undefined> {
    return undefined;
  }
  async getAiGeneratedImagesByUserId(userId: number): Promise<AiGeneratedImage[]> {
    return [];
  }
  async createAiGeneratedImage(image: InsertAiGeneratedImage): Promise<AiGeneratedImage> {
    return { id: Date.now(), ...image } as AiGeneratedImage;
  }
  async deleteAiGeneratedImage(id: number): Promise<boolean> {
    return false;
  }
}

// PostgreSQL Session Store
const PostgresSessionStore = connectPgSimple(session);

// Database Implementation
export class DatabaseStorage implements IStorage {
  public sessionStore: session.Store;

  constructor() {
    try {
      const PostgresSessionStore = connectPgSimple(session);
      this.sessionStore = new PostgresSessionStore({
        pool,
        createTableIfMissing: true,
        tableName: 'session',
        schemaName: 'public',
        errorLog: (error) => {
          console.warn('Session store error, falling back to memory store:', error);
        },
      });
      
      // Add fallback to memory store if PostgreSQL session store fails
      const originalGet = this.sessionStore.get;
      this.sessionStore.get = (sid, callback) => {
        try {
          originalGet.call(this.sessionStore, sid, (err, session) => {
            if (err) {
              console.warn('Session store get error, using empty session:', err);
              // Return empty session instead of error
              callback(null, null);
            } else {
              callback(null, session);
            }
          });
        } catch (error) {
          console.error('Critical session store error:', error);
          callback(null, null);
        }
      };
    } catch (error) {
      console.warn('Failed to create PostgreSQL session store, using memory store:', error);
      // Fallback to memory store if PostgreSQL session store fails
      this.sessionStore = new MemoryStore({
        checkPeriod: 86400000 // Prune expired entries every 24h
      });
    }
  }
  
  async getUserByEmail(email: string): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.email, email));
    return user || undefined;
  }
  
  async setPasswordResetToken(email: string): Promise<string | null> {
    try {
      const user = await this.getUserByEmail(email);
      
      if (!user) {
        return null;
      }
      
      // Generate a random token
      const resetToken = crypto.randomBytes(32).toString('hex');
      
      // Set expiration to 1 hour from now
      const resetPasswordExpiry = new Date();
      resetPasswordExpiry.setHours(resetPasswordExpiry.getHours() + 1);
      
      // Update user with reset token and expiry
      await db.update(users)
        .set({
          resetPasswordToken: resetToken,
          resetPasswordExpiry
        })
        .where(eq(users.id, user.id));
      
      return resetToken;
    } catch (error) {
      console.error('Error setting password reset token:', error);
      return null;
    }
  }
  
  async getUserByResetToken(token: string): Promise<User | undefined> {
    try {
      const [user] = await db.select()
        .from(users)
        .where(eq(users.resetPasswordToken, token));
      
      if (!user) {
        return undefined;
      }
      
      // Check if token is expired
      if (user.resetPasswordExpiry && new Date() > new Date(user.resetPasswordExpiry)) {
        // Token is expired, clear it
        await db.update(users)
          .set({
            resetPasswordToken: null,
            resetPasswordExpiry: null
          })
          .where(eq(users.id, user.id));
          
        return undefined;
      }
      
      return user;
    } catch (error) {
      console.error('Error getting user by reset token:', error);
      return undefined;
    }
  }
  
  async resetPassword(token: string, newPassword: string): Promise<boolean> {
    try {
      const user = await this.getUserByResetToken(token);
      
      if (!user) {
        return false;
      }
      
      // Hash the new password
      const hashedPassword = await AuthService.hashPassword(newPassword);
      
      // Update user with new password and clear reset token
      await db.update(users)
        .set({
          password: hashedPassword,
          resetPasswordToken: null,
          resetPasswordExpiry: null
        })
        .where(eq(users.id, user.id));
      
      return true;
    } catch (error) {
      console.error('Error resetting password:', error);
      return false;
    }
  }

  // User operations
  async getUser(id: number): Promise<User | undefined> {
    const result = await db.select().from(users).where(eq(users.id, id));
    return result[0];
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    const result = await db.select().from(users).where(eq(users.username, username));
    return result[0];
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const result = await db.insert(users).values({
      username: insertUser.username,
      email: insertUser.email,
      password: insertUser.password,
      name: insertUser.name,
      emailVerified: insertUser.emailVerified || false,
      avatarUrl: insertUser.avatarUrl,
      googleId: insertUser.googleId,
      authProvider: insertUser.authProvider || 'local',
      plan: insertUser.plan || 'free',
      role: insertUser.role || 'user',
      createdAt: new Date()
    }).returning();
    return result[0];
  }

  async updateUser(id: number, userUpdate: Partial<User>): Promise<User | undefined> {
    const result = await db.update(users)
      .set(userUpdate)
      .where(eq(users.id, id))
      .returning();
    return result[0];
  }

  async updateStripeCustomerId(userId: number, customerId: string): Promise<User | undefined> {
    const result = await db.update(users)
      .set({ stripeCustomerId: customerId })
      .where(eq(users.id, userId))
      .returning();
    return result[0];
  }

  async updateUserStripeInfo(userId: number, stripeInfo: { customerId: string, subscriptionId: string }): Promise<User | undefined> {
    const result = await db.update(users)
      .set({
        stripeCustomerId: stripeInfo.customerId,
        stripeSubscriptionId: stripeInfo.subscriptionId
      })
      .where(eq(users.id, userId))
      .returning();
    return result[0];
  }

  // Course operations
  async getCourse(id: number): Promise<Course | undefined> {
    const result = await db.select().from(courses).where(eq(courses.id, id));
    if (!result[0]) return undefined;
    
    const course = result[0];
    
    // If the course is published, check for user progress records
    if (course.status === 'published' && course.userId) {
      try {
        // Get progress records for this course
        const progressRecords = await db.select()
          .from(userCourseProgress)
          .where(and(
            eq(userCourseProgress.userId, course.userId),
            eq(userCourseProgress.courseId, course.id)
          ));
        
        // If there's a progress record, use its completion percentage and status
        if (progressRecords.length > 0) {
          const progress = progressRecords[0];
          return {
            ...course,
            completion: parseInt(progress.completionPercentage?.toString() || "0") || 0,
            progressStatus: progress.status || "not_started"
          };
        }
        
        // If no progress record exists, create one with 0% completion and 'not_started' status
        await this.createUserCourseProgress({
          userId: course.userId,
          courseId: course.id,
          completionPercentage: "0",
          isCompleted: false,
          status: "not_started",
          enrolledAt: new Date(),
          lastAccessedAt: new Date()
        });
        
        // Return the course with default 'not_started' status
        return {
          ...course,
          completion: 0,
          progressStatus: "not_started"
        };
      } catch (error) {
        console.error(`Failed to process progress for course ${id}:`, error);
      }
    }
    
    // For non-published courses, return with default status
    return {
      ...course,
      progressStatus: "not_started"
    };
  }

  async getCoursesByUserId(userId: number): Promise<Course[]> {
    // First get all courses created by the user
    const userCourses = await db.select().from(courses).where(eq(courses.userId, userId));
    
    // For each course, get the user's progress
    const coursesWithProgress = await Promise.all(
      userCourses.map(async (course) => {
        // Check if a progress record exists for this course
        const progressRecords = await db.select()
          .from(userCourseProgress)
          .where(and(
            eq(userCourseProgress.userId, userId),
            eq(userCourseProgress.courseId, course.id)
          ));
        
        // If there's a progress record, use its completion percentage
        if (progressRecords.length > 0) {
          const progress = progressRecords[0];
          return {
            ...course,
            completion: parseInt(progress.completionPercentage?.toString() || "0") || 0,
            progressStatus: progress.isCompleted ? "completed" : (parseInt(progress.completionPercentage?.toString() || "0") > 0 ? "in_progress" : "not_started")
          };
        }
        
        // If the course is published but no progress record exists, 
        // create one with 0% completion to track future progress
        if (course.status === 'published') {
          try {
            await this.createUserCourseProgress({
              userId: userId,
              courseId: course.id,
              completionPercentage: "0",
              isCompleted: false,
              enrolledAt: new Date(),
              lastAccessedAt: new Date()
            });
          } catch (error) {
            console.error(`Failed to create progress record for course ${course.id}:`, error);
          }
        }
        
        // Return the course with a default 'not_started' status for unpublished courses
        return {
          ...course,
          progressStatus: "not_started"
        };
      })
    );
    
    return coursesWithProgress;
  }

  async createCourse(insertCourse: InsertCourse): Promise<Course> {
    const result = await db.insert(courses).values({
      ...insertCourse,
      createdAt: new Date(),
      updatedAt: new Date()
    }).returning();
    return result[0];
  }

  async updateCourse(id: number, courseUpdate: Partial<Course>): Promise<Course | undefined> {
    const result = await db.update(courses)
      .set({
        ...courseUpdate,
        updatedAt: new Date()
      })
      .where(eq(courses.id, id))
      .returning();
    return result[0];
  }

  async deleteCourse(id: number): Promise<boolean> {
    const result = await db.delete(courses).where(eq(courses.id, id)).returning();
    return result.rowCount > 0;
  }
  
  // Module operations
  async getModule(id: number): Promise<Module | undefined> {
    const result = await db.select().from(modules).where(eq(modules.id, id));
    return result[0];
  }

  async getModulesByCourseId(courseId: number): Promise<Module[]> {
    return await db.select()
      .from(modules)
      .where(eq(modules.courseId, courseId))
      .orderBy(asc(modules.order));
  }

  async createModule(insertModule: InsertModule): Promise<Module> {
    const result = await db.insert(modules).values({
      ...insertModule,
      createdAt: new Date(),
      updatedAt: new Date()
    }).returning();
    return result[0];
  }

  async updateModule(id: number, moduleUpdate: Partial<Module>): Promise<Module | undefined> {
    const result = await db.update(modules)
      .set({
        ...moduleUpdate,
        updatedAt: new Date()
      })
      .where(eq(modules.id, id))
      .returning();
    return result[0];
  }

  async deleteModule(id: number): Promise<boolean> {
    const result = await db.delete(modules).where(eq(modules.id, id)).returning();
    return result.rowCount > 0;
  }

  // Lesson operations
  async getLesson(id: number): Promise<Lesson | undefined> {
    const result = await db.select().from(lessons).where(eq(lessons.id, id));
    return result[0];
  }

  async getLessonsByCourseId(courseId: number): Promise<Lesson[]> {
    return await db.select()
      .from(lessons)
      .where(eq(lessons.courseId, courseId))
      .orderBy(asc(lessons.order));
  }

  async createLesson(insertLesson: InsertLesson): Promise<Lesson> {
    const result = await db.insert(lessons).values(insertLesson).returning();
    return result[0];
  }

  async updateLesson(id: number, lessonUpdate: Partial<Lesson>): Promise<Lesson | undefined> {
    const result = await db.update(lessons)
      .set(lessonUpdate)
      .where(eq(lessons.id, id))
      .returning();
    return result[0];
  }

  async deleteLesson(id: number): Promise<boolean> {
    const result = await db.delete(lessons).where(eq(lessons.id, id)).returning();
    return result.rowCount > 0;
  }
  
  // getLessonsByModuleId
  async getLessonsByModuleId(moduleId: number): Promise<Lesson[]> {
    return await db.select()
      .from(lessons)
      .where(eq(lessons.moduleId, moduleId))
      .orderBy(asc(lessons.order));
  }

  // Media operations
  async getMedia(id: number): Promise<Media | undefined> {
    const result = await db.select().from(mediaLibrary).where(eq(mediaLibrary.id, id));
    return result[0];
  }

  async getMediaByUserId(userId: number): Promise<Media[]> {
    return await db.select().from(mediaLibrary).where(eq(mediaLibrary.userId, userId));
  }
  
  async getMediaById(id: number): Promise<Media | undefined> {
    const result = await db.select().from(mediaLibrary).where(eq(mediaLibrary.id, id));
    return result[0];
  }
  
  async getMediaBySourceId(sourceId: string): Promise<Media | undefined> {
    const result = await db.select().from(mediaLibrary).where(eq(mediaLibrary.sourceId, sourceId));
    return result[0];
  }
  
  async getRandomMedia(count: number, types?: string[]): Promise<Media[]> {
    let query = db.select().from(mediaLibrary);
    
    // Filter by type if specified
    if (types && types.length > 0) {
      query = query.where(inArray(mediaLibrary.type, types));
    }
    
    // Get a random selection by ordering randomly and limiting
    const result = await query.orderBy(sql`RANDOM()`).limit(count);
    
    return result;
  }

  async createMedia(insertMedia: InsertMedia): Promise<Media> {
    const result = await db.insert(mediaLibrary).values({
      ...insertMedia,
      createdAt: new Date()
    }).returning();
    return result[0];
  }

  async deleteMedia(id: number): Promise<boolean> {
    const result = await db.delete(mediaLibrary).where(eq(mediaLibrary.id, id)).returning();
    return result.rowCount > 0;
  }
  
  async updateMedia(id: number, updates: Partial<Media>): Promise<Media | undefined> {
    // Remove id from updates if it exists
    const { id: _, ...validUpdates } = updates;
    
    const result = await db.update(mediaLibrary)
      .set(validUpdates)
      .where(eq(mediaLibrary.id, id))
      .returning();
      
    return result[0];
  }

  // Integration operations
  async getIntegrationsByUserId(userId: number): Promise<Integration[]> {
    return await db.select().from(integrations).where(eq(integrations.userId, userId));
  }

  async createIntegration(insertIntegration: InsertIntegration): Promise<Integration> {
    const result = await db.insert(integrations).values({
      ...insertIntegration,
      createdAt: new Date(),
      updatedAt: new Date()
    }).returning();
    return result[0];
  }

  async updateIntegration(id: number, integrationUpdate: Partial<Integration>): Promise<Integration | undefined> {
    const result = await db.update(integrations)
      .set({
        ...integrationUpdate,
        updatedAt: new Date()
      })
      .where(eq(integrations.id, id))
      .returning();
    return result[0];
  }

  // User stats operations
  async getUserStats(userId: number): Promise<UserStats | undefined> {
    const result = await db.select().from(userStats).where(eq(userStats.userId, userId));
    return result[0];
  }

  async createUserStats(insertUserStats: InsertUserStats): Promise<UserStats> {
    const result = await db.insert(userStats).values(insertUserStats).returning();
    return result[0];
  }

  async updateUserStats(userId: number, statsUpdate: Partial<UserStats>): Promise<UserStats | undefined> {
    const result = await db.update(userStats)
      .set(statsUpdate)
      .where(eq(userStats.userId, userId))
      .returning();
    return result[0];
  }

  // Template operations
  async seedTemplates() {
    // First, let's delete all existing templates to avoid duplicates
    await db.delete(templates);
    
    // Now seed with fresh data
    console.log("Seeding templates with fresh data");
    
    // Define template data
    const templateData = [
      // AI Templates
      {
        name: "Quick Course Generator",
        description: "Generate a complete course structure from just a title and description",
        icon: "ri-robot-line",
        type: "course_generator",
        category: null,
        structure: null
      },
      {
        name: "Script Writer",
        description: "Create engaging lesson scripts with AI assistance",
        icon: "ri-file-text-line",
        type: "script_generator",
        category: null,
        structure: null
      },
      {
        name: "AI Voiceover",
        description: "Convert your scripts to natural-sounding voiceovers",
        icon: "ri-voice-recognition-line",
        type: "voice_generator",
        category: null,
        structure: null
      },
      
      // Course Templates
      {
        name: "Marketing Master Class",
        description: "Professional template for digital marketing courses",
        icon: "ri-flight-takeoff-line",
        type: "course_template",
        category: "marketing",
        structure: {
          modules: [
            {
              title: "Marketing Fundamentals",
              description: "Core concepts and principles of modern digital marketing",
              lessons: [
                {
                  title: "Introduction to Digital Marketing",
                  description: "Overview of digital marketing channels and strategies"
                },
                {
                  title: "Developing a Marketing Strategy",
                  description: "How to create an effective digital marketing plan"
                }
              ]
            }
          ]
        }
      },
      {
        name: "Personal Development",
        description: "Template for self-improvement and growth courses",
        icon: "ri-mental-health-line",
        type: "course_template",
        category: "personal_development",
        structure: {
          modules: [
            {
              title: "Understanding Yourself",
              description: "Self-awareness and personal discovery techniques",
              lessons: [
                {
                  title: "Self-Assessment Methods",
                  description: "Tools and techniques for understanding your strengths and weaknesses"
                },
                {
                  title: "Setting Meaningful Goals",
                  description: "How to create goals that align with your values and aspirations"
                }
              ]
            }
          ]
        }
      },
      {
        name: "Business Management",
        description: "Professional template for corporate training",
        icon: "ri-building-line",
        type: "course_template",
        category: "business",
        structure: {
          modules: [
            {
              title: "Leadership Essentials",
              description: "Core principles of effective leadership",
              lessons: [
                {
                  title: "Leadership Styles",
                  description: "Understanding different approaches to leadership"
                },
                {
                  title: "Effective Team Management",
                  description: "Strategies for building and leading successful teams"
                }
              ]
            }
          ]
        }
      }
    ];
    
    // Insert templates into database
    await db.insert(templates).values(templateData);
  }

  async getAllTemplates(): Promise<Template[]> {
    // Let's force regeneration of templates to ensure all types are available
    await this.seedTemplates();
    return await db.select().from(templates);
  }

  async getTemplate(id: number): Promise<Template | undefined> {
    const result = await db.select().from(templates).where(eq(templates.id, id));
    if (result.length === 0) {
      // Check if templates need to be seeded
      const allTemplates = await db.select().from(templates);
      if (allTemplates.length === 0) {
        await this.seedTemplates();
        return await this.getTemplate(id);
      }
      return undefined;
    }
    return result[0];
  }

  // Template History operations
  async getTemplateHistory(id: number): Promise<TemplateHistory | undefined> {
    const result = await db.select().from(templateHistory).where(eq(templateHistory.id, id));
    return result[0];
  }

  async getTemplateHistoryByUserId(userId: number): Promise<TemplateHistory[]> {
    return await db.select()
      .from(templateHistory)
      .where(eq(templateHistory.userId, userId))
      .orderBy(desc(templateHistory.createdAt));
  }

  async createTemplateHistory(insertTemplateHistory: InsertTemplateHistory): Promise<TemplateHistory> {
    const result = await db.insert(templateHistory).values({
      ...insertTemplateHistory,
      createdAt: new Date()
    }).returning();
    return result[0];
  }

  async updateTemplateHistoryFavorite(id: number, favorited: boolean): Promise<TemplateHistory | undefined> {
    const result = await db.update(templateHistory)
      .set({ favorited })
      .where(eq(templateHistory.id, id))
      .returning();
    return result[0];
  }

  // Team operations
  async getTeam(id: number): Promise<Team | undefined> {
    const result = await db.select().from(teams).where(eq(teams.id, id));
    return result[0];
  }

  async getTeamsByUserId(userId: number): Promise<Team[]> {
    // Get teams where user is a member
    const teamMembersResult = await db.select()
      .from(teamMembers)
      .where(eq(teamMembers.userId, userId));
    
    const teamIds = teamMembersResult.map(tm => tm.teamId);
    
    // Also get teams where user is the owner
    const ownerTeamsResult = await db.select()
      .from(teams)
      .where(eq(teams.ownerId, userId));
    
    const ownerTeamIds = ownerTeamsResult.map(team => team.id);
    
    // Combine the two sets of team IDs
    const allTeamIds = [...new Set([...teamIds, ...ownerTeamIds])];
    
    if (allTeamIds.length === 0) {
      return [];
    }
    
    // Get all teams
    return await db.select()
      .from(teams)
      .where(inArray(teams.id, allTeamIds));
  }

  async createTeam(insertTeam: InsertTeam): Promise<Team> {
    const result = await db.insert(teams).values({
      ...insertTeam,
      createdAt: new Date(),
      updatedAt: new Date()
    }).returning();
    return result[0];
  }

  async updateTeam(id: number, teamUpdate: Partial<Team>): Promise<Team | undefined> {
    const result = await db.update(teams)
      .set({
        ...teamUpdate,
        updatedAt: new Date()
      })
      .where(eq(teams.id, id))
      .returning();
    return result[0];
  }

  async deleteTeam(id: number): Promise<boolean> {
    const result = await db.delete(teams).where(eq(teams.id, id)).returning();
    return result.length > 0;
  }

  // Team Members operations
  async getTeamMembers(teamId: number): Promise<TeamMember[]> {
    return await db.select()
      .from(teamMembers)
      .where(eq(teamMembers.teamId, teamId));
  }

  async getTeamMembersByUserId(userId: number): Promise<TeamMember[]> {
    return await db.select()
      .from(teamMembers)
      .where(eq(teamMembers.userId, userId));
  }

  async addTeamMember(insertTeamMember: InsertTeamMember): Promise<TeamMember> {
    const result = await db.insert(teamMembers).values({
      ...insertTeamMember,
      joinedAt: new Date()
    }).returning();
    return result[0];
  }

  async updateTeamMemberRole(teamId: number, userId: number, role: string): Promise<TeamMember | undefined> {
    const result = await db.update(teamMembers)
      .set({ role })
      .where(and(
        eq(teamMembers.teamId, teamId),
        eq(teamMembers.userId, userId)
      ))
      .returning();
    return result[0];
  }

  async removeTeamMember(teamId: number, userId: number): Promise<boolean> {
    const result = await db.delete(teamMembers)
      .where(and(
        eq(teamMembers.teamId, teamId),
        eq(teamMembers.userId, userId)
      ))
      .returning();
    return result.length > 0;
  }

  // Course Collaborators operations
  async getCourseCollaborators(courseId: number): Promise<CourseCollaborator[]> {
    return await db.select()
      .from(courseCollaborators)
      .where(eq(courseCollaborators.courseId, courseId));
  }

  async getUserCollaboratedCourses(userId: number): Promise<Course[]> {
    const collaborations = await db.select()
      .from(courseCollaborators)
      .where(eq(courseCollaborators.userId, userId));

    if (collaborations.length === 0) {
      return [];
    }

    const courseIds = collaborations.map(collab => collab.courseId);
    
    return await db.select()
      .from(courses)
      .where(inArray(courses.id, courseIds));
  }

  async addCourseCollaborator(insertCollaborator: InsertCourseCollaborator): Promise<CourseCollaborator> {
    const result = await db.insert(courseCollaborators).values({
      ...insertCollaborator,
      addedAt: new Date()
    }).returning();
    return result[0];
  }

  async updateCourseCollaborator(courseId: number, userId: number, update: Partial<CourseCollaborator>): Promise<CourseCollaborator | undefined> {
    const result = await db.update(courseCollaborators)
      .set(update)
      .where(and(
        eq(courseCollaborators.courseId, courseId),
        eq(courseCollaborators.userId, userId)
      ))
      .returning();
    return result[0];
  }

  async removeCourseCollaborator(courseId: number, userId: number): Promise<boolean> {
    const result = await db.delete(courseCollaborators)
      .where(and(
        eq(courseCollaborators.courseId, courseId),
        eq(courseCollaborators.userId, userId)
      ))
      .returning();
    return result.length > 0;
  }

  // Team Courses operations
  async getTeamCourses(teamId: number): Promise<Course[]> {
    const teamCoursesResult = await db.select()
      .from(teamCourses)
      .where(eq(teamCourses.teamId, teamId));
    
    if (teamCoursesResult.length === 0) {
      return [];
    }
    
    const courseIds = teamCoursesResult.map(tc => tc.courseId);
    
    return await db.select()
      .from(courses)
      .where(inArray(courses.id, courseIds));
  }

  async addCourseToTeam(insertTeamCourse: InsertTeamCourse): Promise<TeamCourse> {
    const result = await db.insert(teamCourses).values({
      ...insertTeamCourse,
      addedAt: new Date()
    }).returning();
    return result[0];
  }

  async removeCourseFromTeam(teamId: number, courseId: number): Promise<boolean> {
    const result = await db.delete(teamCourses)
      .where(and(
        eq(teamCourses.teamId, teamId),
        eq(teamCourses.courseId, courseId)
      ))
      .returning();
    return result.length > 0;
  }

  // Course Analytics operations
  async getCourseAnalytics(courseId: number): Promise<CourseAnalytics | undefined> {
    const result = await db.select()
      .from(courseAnalytics)
      .where(eq(courseAnalytics.courseId, courseId));
    return result[0];
  }

  async createCourseAnalytics(insertAnalytics: InsertCourseAnalytics): Promise<CourseAnalytics> {
    const result = await db.insert(courseAnalytics).values({
      ...insertAnalytics,
      createdAt: new Date(),
      updatedAt: new Date()
    }).returning();
    return result[0];
  }

  async updateCourseAnalytics(courseId: number, analyticsUpdate: Partial<CourseAnalytics>): Promise<CourseAnalytics | undefined> {
    const result = await db.update(courseAnalytics)
      .set({
        ...analyticsUpdate,
        updatedAt: new Date()
      })
      .where(eq(courseAnalytics.courseId, courseId))
      .returning();
    return result[0];
  }

  // Lesson Analytics operations
  async getLessonAnalytics(lessonId: number): Promise<LessonAnalytics | undefined> {
    const result = await db.select()
      .from(lessonAnalytics)
      .where(eq(lessonAnalytics.lessonId, lessonId));
    return result[0];
  }

  async getLessonAnalyticsByCourseId(courseId: number): Promise<LessonAnalytics[]> {
    const courseLessons = await this.getLessonsByCourseId(courseId);
    
    if (courseLessons.length === 0) {
      return [];
    }
    
    const lessonIds = courseLessons.map(lesson => lesson.id);
    
    return await db.select()
      .from(lessonAnalytics)
      .where(inArray(lessonAnalytics.lessonId, lessonIds));
  }

  async createLessonAnalytics(insertAnalytics: InsertLessonAnalytics): Promise<LessonAnalytics> {
    const result = await db.insert(lessonAnalytics).values({
      ...insertAnalytics,
      createdAt: new Date(),
      updatedAt: new Date()
    }).returning();
    return result[0];
  }

  async updateLessonAnalytics(lessonId: number, analyticsUpdate: Partial<LessonAnalytics>): Promise<LessonAnalytics | undefined> {
    const result = await db.update(lessonAnalytics)
      .set({
        ...analyticsUpdate,
        updatedAt: new Date()
      })
      .where(eq(lessonAnalytics.lessonId, lessonId))
      .returning();
    return result[0];
  }

  // User Course Progress operations
  async getUserCourseProgress(userId: number, courseId: number): Promise<UserCourseProgress | undefined> {
    const result = await db.select()
      .from(userCourseProgress)
      .where(and(
        eq(userCourseProgress.userId, userId),
        eq(userCourseProgress.courseId, courseId)
      ));
    return result[0];
  }

  async getUserCourseProgressByUserId(userId: number): Promise<UserCourseProgress[]> {
    // Get course progress without referencing the status column which is not in the database
    return await db.select()
      .from(userCourseProgress)
      .where(eq(userCourseProgress.userId, userId));
  }

  async getUsersByCourseId(courseId: number): Promise<UserCourseProgress[]> {
    return await db.select()
      .from(userCourseProgress)
      .where(eq(userCourseProgress.courseId, courseId));
  }

  async createUserCourseProgress(insertProgress: InsertUserCourseProgress): Promise<UserCourseProgress> {
    // No status field in the actual database schema
    const result = await db.insert(userCourseProgress).values({
      ...insertProgress,
      enrolledAt: insertProgress.enrolledAt || new Date(),
      lastAccessedAt: insertProgress.lastAccessedAt || new Date()
    }).returning();
    
    return result[0];
  }

  async updateUserCourseProgress(userId: number, courseId: number, progressUpdate: Partial<UserCourseProgress>): Promise<UserCourseProgress | undefined> {
    // No status field in the schema, only update valid fields
    const result = await db.update(userCourseProgress)
      .set({
        ...progressUpdate,
        lastAccessedAt: new Date()
      })
      .where(and(
        eq(userCourseProgress.userId, userId),
        eq(userCourseProgress.courseId, courseId)
      ))
      .returning();
    return result[0];
  }

  // User Lesson Progress operations
  async getUserLessonProgress(userId: number, lessonId: number): Promise<UserLessonProgress | undefined> {
    const result = await db.select()
      .from(userLessonProgress)
      .where(and(
        eq(userLessonProgress.userId, userId),
        eq(userLessonProgress.lessonId, lessonId)
      ));
    return result[0];
  }

  async getUserLessonProgressByCourseId(userId: number, courseId: number): Promise<UserLessonProgress[]> {
    return await db.select()
      .from(userLessonProgress)
      .where(and(
        eq(userLessonProgress.userId, userId),
        eq(userLessonProgress.courseId, courseId)
      ));
  }

  async createUserLessonProgress(insertProgress: InsertUserLessonProgress): Promise<UserLessonProgress> {
    const result = await db.insert(userLessonProgress).values({
      ...insertProgress,
      startedAt: new Date(),
      lastAccessedAt: new Date()
    }).returning();
    return result[0];
  }

  async updateUserLessonProgress(userId: number, lessonId: number, progressUpdate: Partial<UserLessonProgress>): Promise<UserLessonProgress | undefined> {
    const result = await db.update(userLessonProgress)
      .set({
        ...progressUpdate,
        lastAccessedAt: new Date()
      })
      .where(and(
        eq(userLessonProgress.userId, userId),
        eq(userLessonProgress.lessonId, lessonId)
      ))
      .returning();
    return result[0];
  }

  // Analytics Events operations
  async recordAnalyticsEvent(insertEvent: InsertAnalyticsEvent): Promise<AnalyticsEvent> {
    const result = await db.insert(analyticsEvents).values({
      ...insertEvent,
      createdAt: new Date()
    }).returning();
    return result[0];
  }

  async getAnalyticsEventsByCourseId(courseId: number, limit = 100): Promise<AnalyticsEvent[]> {
    return await db.select()
      .from(analyticsEvents)
      .where(eq(analyticsEvents.courseId, courseId))
      .orderBy(desc(analyticsEvents.createdAt))
      .limit(limit);
  }

  async getAnalyticsEventsByUserId(userId: number, limit = 100): Promise<AnalyticsEvent[]> {
    return await db.select()
      .from(analyticsEvents)
      .where(eq(analyticsEvents.userId, userId))
      .orderBy(desc(analyticsEvents.createdAt))
      .limit(limit);
  }

  async getAnalyticsEventsByLessonId(lessonId: number, limit = 100): Promise<AnalyticsEvent[]> {
    return await db.select()
      .from(analyticsEvents)
      .where(eq(analyticsEvents.lessonId, lessonId))
      .orderBy(desc(analyticsEvents.createdAt))
      .limit(limit);
  }

  // Publishing operations
  async getPublishingByCourseId(courseId: number): Promise<Publishing[]> {
    return await db.select()
      .from(publishing)
      .where(eq(publishing.courseId, courseId));
  }

  async getPublishingByPlatform(platform: string): Promise<Publishing[]> {
    return await db.select()
      .from(publishing)
      .where(eq(publishing.platform, platform));
  }

  async createPublishing(insertPublishing: InsertPublishing): Promise<Publishing> {
    const result = await db.insert(publishing).values({
      ...insertPublishing,
      publishedAt: new Date(),
      createdAt: new Date(),
      updatedAt: new Date()
    }).returning();
    return result[0];
  }

  async updatePublishing(id: number, publishingUpdate: Partial<Publishing>): Promise<Publishing | undefined> {
    const result = await db.update(publishing)
      .set({
        ...publishingUpdate,
        updatedAt: new Date()
      })
      .where(eq(publishing.id, id))
      .returning();
    return result[0];
  }

  async deletePublishing(id: number): Promise<boolean> {
    const result = await db.delete(publishing).where(eq(publishing.id, id)).returning();
    return result.length > 0;
  }

  // Dashboard Analytics
  async getCoursePerformanceOverview(courseId: number): Promise<any> {
    const course = await this.getCourse(courseId);
    if (!course) {
      throw new Error('Course not found');
    }

    const analytics = await this.getCourseAnalytics(courseId);
    const lessonAnalytics = await this.getLessonAnalyticsByCourseId(courseId);
    const enrollments = await this.getUsersByCourseId(courseId);
    
    return {
      course,
      analytics: analytics || {
        totalViews: 0,
        totalCompletions: 0,
        averageRating: 0,
        ratingsCount: 0,
        studentsEnrolled: 0,
        averageCompletionTime: 0
      },
      lessonAnalytics,
      enrollments
    };
  }

  async getUserEngagementStats(userId: number): Promise<any> {
    const user = await this.getUser(userId);
    if (!user) {
      throw new Error('User not found');
    }

    const userStats = await this.getUserStats(userId);
    const courseProgress = await this.getUserCourseProgressByUserId(userId);
    const enrolledCourses = await Promise.all(
      courseProgress.map(progress => this.getCourse(progress.courseId))
    );
    
    return {
      user,
      stats: userStats,
      courseProgress,
      enrolledCourses: enrolledCourses.filter(Boolean)
    };
  }

  async getPopularCourses(limit = 10): Promise<any[]> {
    // Get all course analytics and sort by enrollments
    const allAnalytics = await db.select().from(courseAnalytics);
    
    // Sort by students enrolled in descending order
    const sortedAnalytics = allAnalytics.sort((a, b) => 
      b.studentsEnrolled - a.studentsEnrolled
    ).slice(0, limit);
    
    // Get course details for each analytics entry
    return Promise.all(
      sortedAnalytics.map(async analytics => {
        const course = await this.getCourse(analytics.courseId);
        return {
          ...analytics,
          course
        };
      })
    );
  }

  // AI Generated Images operations
  async getAiGeneratedImage(id: number): Promise<AiGeneratedImage | undefined> {
    const result = await db.select().from(aiGeneratedImages).where(eq(aiGeneratedImages.id, id));
    return result[0];
  }

  async getAiGeneratedImagesByUserId(userId: number): Promise<AiGeneratedImage[]> {
    return await db.select()
      .from(aiGeneratedImages)
      .where(eq(aiGeneratedImages.userId, userId))
      .orderBy(desc(aiGeneratedImages.createdAt));
  }

  async createAiGeneratedImage(insertImage: InsertAiGeneratedImage): Promise<AiGeneratedImage> {
    const result = await db.insert(aiGeneratedImages).values({
      ...insertImage,
      createdAt: new Date()
    }).returning();
    return result[0];
  }

  async deleteAiGeneratedImage(id: number): Promise<boolean> {
    const result = await db.delete(aiGeneratedImages).where(eq(aiGeneratedImages.id, id)).returning();
    return result.length > 0;
  }

  // Generated Videos operations
  async getGeneratedVideo(id: string): Promise<GeneratedVideo | undefined> {
    const result = await db.select().from(generatedVideos).where(eq(generatedVideos.id, id));
    return result[0];
  }

  async getGeneratedVideosByUserId(userId: number): Promise<GeneratedVideo[]> {
    return await db.select()
      .from(generatedVideos)
      .where(eq(generatedVideos.userId, userId))
      .orderBy(desc(generatedVideos.createdAt));
  }

  async createGeneratedVideo(insertVideo: InsertGeneratedVideo): Promise<GeneratedVideo> {
    // Ensure all required fields are included with proper defaults for optional ones
    const videoData = {
      ...insertVideo,
      createdAt: new Date(),
      courseId: insertVideo.courseId || null,
      lessonId: insertVideo.lessonId || null,
      mediaId: insertVideo.mediaId || null,
      videoUrl: insertVideo.videoUrl || null,
      thumbnailUrl: insertVideo.thumbnailUrl || null,
      error: insertVideo.error || null,
      duration: insertVideo.duration || null,
      processingStatus: insertVideo.processingStatus || 'pending',
      animationKeyframes: insertVideo.animationKeyframes || null
    };
    
    const result = await db.insert(generatedVideos)
      .values(videoData)
      .returning();
    
    // If this is an AI-generated video (not a free import), update user's AI credits
    if (!insertVideo.id.startsWith('free-')) {
      const stats = await this.getUserStats(insertVideo.userId);
      if (stats) {
        // Deduct 200 AI credits for AI video generation
        const aiCreditsToDeduct = 200;
        await this.updateUserStats(insertVideo.userId, {
          aiCredits: Math.max(0, (stats.aiCredits || 0) - aiCreditsToDeduct)
        });
      }
    }
    
    return result[0];
  }

  async updateGeneratedVideo(id: string, videoUpdate: Partial<GeneratedVideo>): Promise<GeneratedVideo | undefined> {
    const result = await db.update(generatedVideos)
      .set(videoUpdate)
      .where(eq(generatedVideos.id, id))
      .returning();
    return result[0];
  }

  async deleteGeneratedVideo(id: string): Promise<boolean> {
    const video = await this.getGeneratedVideo(id);
    if (!video) return false;
    
    // If the video has an associated media entry, delete that too
    if (video.mediaId) {
      await this.deleteMedia(video.mediaId);
    }
    
    const result = await db.delete(generatedVideos)
      .where(eq(generatedVideos.id, id))
      .returning();
    return result.length > 0;
  }

  // Mini Courses operations
  async getMiniCourse(id: number): Promise<MiniCourse | undefined> {
    const [miniCourse] = await db.select().from(miniCourses).where(eq(miniCourses.id, id));
    return miniCourse || undefined;
  }

  async getMiniCoursesByUserId(userId: number): Promise<MiniCourse[]> {
    return await db.select().from(miniCourses).where(eq(miniCourses.userId, userId));
  }

  async createMiniCourse(insertMiniCourse: InsertMiniCourse): Promise<MiniCourse> {
    const [miniCourse] = await db.insert(miniCourses)
      .values(insertMiniCourse)
      .returning();
    return miniCourse;
  }

  async updateMiniCourse(id: number, update: Partial<MiniCourse>): Promise<MiniCourse | undefined> {
    // If status is changing to published and publishedAt isn't set, set it
    if (update.status === 'published') {
      const current = await this.getMiniCourse(id);
      if (current && current.status !== 'published') {
        update.publishedAt = new Date();
      }
    }
    
    const [updatedMiniCourse] = await db.update(miniCourses)
      .set({
        ...update,
        updatedAt: new Date()
      })
      .where(eq(miniCourses.id, id))
      .returning();
    
    return updatedMiniCourse || undefined;
  }

  async deleteMiniCourse(id: number): Promise<boolean> {
    const miniCourse = await this.getMiniCourse(id);
    if (!miniCourse) return false;
    
    // Clean up related resources
    if (miniCourse.scriptId) {
      await this.deleteMedia(miniCourse.scriptId);
    }
    
    if (miniCourse.audioId) {
      await this.deleteMedia(miniCourse.audioId);
    }
    
    if (miniCourse.videoId) {
      await this.deleteMedia(miniCourse.videoId);
    }
    
    const result = await db.delete(miniCourses)
      .where(eq(miniCourses.id, id))
      .returning();
    
    return result.length > 0;
  }
  
  // Quiz operations
  async deleteQuiz(id: number): Promise<boolean> {
    try {
      // First fetch the quiz to check if it exists
      const [quiz] = await db.select()
        .from(quizzes)
        .where(eq(quizzes.id, id));
        
      if (!quiz) {
        return false;
      }
      
      // Delete the quiz - the cascade constraints should take care of deleting 
      // the related questions, answers, and flashcards automatically
      const result = await db.delete(quizzes)
        .where(eq(quizzes.id, id))
        .returning();
      
      return result.length > 0;
    } catch (error) {
      console.error("Error deleting quiz:", error);
      return false;
    }
  }

  // Platform integration operations
  async getAllPlatforms(userId?: number): Promise<any[]> {
  // In a real implementation, these would be stored in the database
  const platforms = [
    {
      id: 1,
      name: "Udemy",
      slug: "udemy",
      icon: "SiUdemy",
      description: "Publish your course to the Udemy marketplace",
      category: "marketplace",
      authType: "apiKey",
      fields: [
        {
          name: "apiKey",
          label: "API Key",
          type: "password",
          required: true
        },
        {
          name: "domain",
          label: "Domain",
          type: "text",
          required: false
        }
      ]
    },
    {
      id: 2,
      name: "Teachable",
      slug: "teachable",
      icon: "SiTeachable",
      description: "Publish to your Teachable school",
      category: "hosted_platform",
      authType: "apiKey",
      fields: [
        {
          name: "apiKey",
          label: "API Key",
          type: "password",
          required: true
        },
        {
          name: "domain",
          label: "School Subdomain",
          type: "text",
          required: true,
          placeholder: "your-school.teachable.com"
        }
      ]
    },
    {
      id: 3,
      name: "Thinkific",
      slug: "thinkific",
      icon: "SiThinkific",
      description: "Publish your course to your Thinkific site",
      category: "hosted_platform",
      authType: "oauth",
      oauthUrl: "/api/auth/oauth/thinkific"
    },
    {
      id: 4,
      name: "YouTube",
      slug: "youtube",
      icon: "SiYoutube",
      description: "Publish your video lessons to YouTube",
      category: "video_platform",
      authType: "oauth",
      oauthUrl: "/api/auth/oauth/youtube"
    },
    {
      id: 5,
      name: "Vimeo",
      slug: "vimeo",
      icon: "SiVimeo",
      description: "Host your course videos on Vimeo",
      category: "video_platform",
      authType: "apiKey",
      fields: [
        {
          name: "accessToken",
          label: "Access Token",
          type: "password",
          required: true
        }
      ]
    },
    {
      id: 6,
      name: "Kajabi",
      slug: "kajabi",
      icon: "SiKajabi",
      description: "Publish your course to your Kajabi site",
      category: "hosted_platform",
      authType: "apiKey",
      fields: [
        {
          name: "apiKey",
          label: "API Key",
          type: "password",
          required: true
        },
        {
          name: "domain",
          label: "Site Domain",
          type: "text",
          required: true,
          placeholder: "your-site.mykajabi.com"
        }
      ]
    },
    // Slack platform has been removed
  ];

  if (userId) {
    // In a real implementation, we would query the database for connections
    // For now, we'll return all platforms as not connected
    return platforms.map(platform => ({
      ...platform,
      connected: false
    }));
  }

  return platforms;
}

async getPlatformBySlug(slug: string): Promise<any | undefined> {
  const platforms = await this.getAllPlatforms();
  return platforms.find(p => p.slug === slug);
}

async connectPlatform(userId: number, platformSlug: string): Promise<boolean> {
  // In a real implementation, this would create a record in the database
  return true;
}

async disconnectPlatform(userId: number, platformId: number): Promise<boolean> {
  // In a real implementation, this would remove a record from the database
  return true;
}

async isPlatformConnected(userId: number, platformSlug: string): Promise<boolean> {
  // In a real implementation, this would check the database
  return false;
}

async publishCourseToPlatform(userId: number, courseId: number, platformSlug: string, options?: any): Promise<any> {
  // In a real implementation, this would handle the publishing logic
  return null;
}

async getCoursePublications(userId: number, courseId: number): Promise<any[]> {
  // In a real implementation, this would query the database
  return [];
}

async isUserCourseCollaborator(userId: number, courseId: number): Promise<boolean> {
  const collaborators = await this.getCourseCollaborators(courseId);
  return collaborators.some(c => c.userId === userId);
}

// Platform integration operations
async createPlatformConnection(data: any): Promise<any> {
  // In a real implementation, this would create a record in the database
  // For the demo, we'll just return a mock object
  return {
    id: Date.now(),
    ...data,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };
}

async getPlatformConnection(id: number): Promise<any> {
  // In a real implementation, this would query the database
  return null;
}

async getPlatformConnectionByUserAndPlatform(userId: number, platformSlug: string): Promise<any> {
  // In a real implementation, this would query the database
  return null;
}

async updatePlatformConnectionLastSynced(id: number): Promise<any> {
  // In a real implementation, this would update a record in the database
  return null;
}

async deletePlatformConnection(id: number): Promise<boolean> {
  // In a real implementation, this would remove a record from the database
  return true;
}

async deductUserCredits(userId: number, creditsToDeduct: number): Promise<boolean> {
  try {
    // Get current user stats
    const stats = await this.getUserStats(userId);
      
    if (!stats || stats.aiCredits === null || stats.aiCredits < creditsToDeduct) {
      return false; // Not enough credits
    }
      
    // Update user stats with reduced credits
    await this.updateUserStats(userId, {
      aiCredits: Math.max(0, stats.aiCredits - creditsToDeduct)
    });
      
    return true;
  } catch (error) {
    console.error("Error deducting user credits:", error);
    return false;
  }
}

// API Keys operations
async getUserApiKeys(userId: number): Promise<UserApiKey[]> {
  return await db.select()
    .from(userApiKeys)
    .where(eq(userApiKeys.userId, userId));
}

async getUserApiKeyByService(userId: number, service: string): Promise<UserApiKey | undefined> {
  const result = await db.select()
    .from(userApiKeys)
    .where(
      and(
        eq(userApiKeys.userId, userId),
        eq(userApiKeys.service, service),
        eq(userApiKeys.isActive, true)
      )
    );
  return result[0];
}

async getUserApiKeyById(id: number): Promise<UserApiKey | undefined> {
  const result = await db.select()
    .from(userApiKeys)
    .where(eq(userApiKeys.id, id));
  return result[0];
}

async saveUserApiKey(apiKey: InsertUserApiKey): Promise<UserApiKey> {
  // Check if the user already has an API key for this service
  const existingKey = await this.getUserApiKeyByService(apiKey.userId, apiKey.service);
  
  if (existingKey) {
    // If exists, update the existing key
    return await this.updateUserApiKey(existingKey.id, {
      apiKey: apiKey.apiKey,
      isVerified: apiKey.isVerified,
      isActive: true,
      lastVerified: apiKey.lastVerified,
      updatedAt: new Date()
    }) as UserApiKey;
  }
  
  // Otherwise, create a new key
  const result = await db.insert(userApiKeys).values({
    ...apiKey,
    createdAt: new Date(),
    updatedAt: new Date()
  }).returning();
  
  return result[0];
}

async updateUserApiKey(id: number, apiKeyUpdate: Partial<UserApiKey>): Promise<UserApiKey | undefined> {
  const result = await db.update(userApiKeys)
    .set({
      ...apiKeyUpdate,
      updatedAt: new Date()
    })
    .where(eq(userApiKeys.id, id))
    .returning();
  
  return result[0];
}

async deleteUserApiKey(id: number): Promise<boolean> {
  const result = await db.delete(userApiKeys)
    .where(eq(userApiKeys.id, id))
    .returning();
  
  return result.length > 0;
}

async verifyUserApiKey(userId: number, service: string, apiKey: string): Promise<boolean> {
  try {
    // Perform verification based on the service
    let isValid = false;
    
    switch (service) {
      case 'openai':
        isValid = await this.verifyOpenAIKey(apiKey);
        break;
      case 'elevenlabs':
        isValid = await this.verifyElevenLabsKey(apiKey);
        break;
      case 'google':
        isValid = await this.verifyGoogleKey(apiKey);
        break;
      default:
        return false;
    }
    
    if (isValid) {
      // Save or update the API key
      await this.saveUserApiKey({
        userId,
        service,
        apiKey,
        isVerified: true,
        isActive: true,
        lastVerified: new Date()
      });
    }
    
    return isValid;
  } catch (error) {
    console.error(`Error verifying ${service} API key:`, error);
    return false;
  }
}

// Helper methods for API key verification
private async verifyOpenAIKey(apiKey: string): Promise<boolean> {
  try {
    // Make a simple API call to verify the key
    const response = await fetch('https://api.openai.com/v1/models', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      }
    });
    
    return response.status === 200;
  } catch (error) {
    console.error('OpenAI key verification error:', error);
    return false;
  }
}

private async verifyElevenLabsKey(apiKey: string): Promise<boolean> {
  try {
    // Make a simple API call to verify the key
    const response = await fetch('https://api.elevenlabs.io/v1/voices', {
      method: 'GET',
      headers: {
        'xi-api-key': apiKey,
        'Content-Type': 'application/json'
      }
    });
    
    return response.status === 200;
  } catch (error) {
    console.error('ElevenLabs key verification error:', error);
    return false;
  }
}

private async verifyGoogleKey(apiKey: string): Promise<boolean> {
  try {
    // Make a simple API call to verify the key
    const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models?key=${apiKey}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    return response.status === 200;
  } catch (error) {
    console.error('Google API key verification error:', error);
    return false;
  }
}

  // TTS (Text-to-Speech) methods
  async saveTTSRecord(record: TTSResponse): Promise<TTSRecord> {
    const result = await db.insert(ttsRecords).values({
      userId: record.userId,
      text: record.text,
      fileName: record.fileName,
      filePath: record.filePath,
      service: record.service,
      wordCount: record.wordCount,
      creditCost: record.creditCost,
      title: record.title || null,
      metadata: record.metadata || {},
      createdAt: new Date()
    }).returning();
    
    return result[0];
  }

  async getTTSHistory(userId: number): Promise<TTSRecord[]> {
    const records = await db.select()
      .from(ttsRecords)
      .where(eq(ttsRecords.userId, userId))
      .orderBy(desc(ttsRecords.createdAt));
    
    return records;
  }

  async getTTSById(id: number): Promise<TTSRecord | undefined> {
    const [record] = await db.select()
      .from(ttsRecords)
      .where(eq(ttsRecords.id, id));
    
    return record;
  }

  async deleteTTSRecord(id: number): Promise<boolean> {
    const result = await db.delete(ttsRecords)
      .where(eq(ttsRecords.id, id))
      .returning();
    
    return result.length > 0;
  }
  
  // Landing Page operations
  async getLandingPage(id: number): Promise<LandingPage | undefined> {
    const [landingPage] = await db.select()
      .from(landingPages)
      .where(eq(landingPages.id, id));
    
    return landingPage;
  }
  
  async getLandingPageBySlug(slug: string): Promise<LandingPage | undefined> {
    const [landingPage] = await db.select()
      .from(landingPages)
      .where(eq(landingPages.slug, slug));
    
    return landingPage;
  }
  
  async getLandingPagesByCourseId(courseId: number): Promise<LandingPage[]> {
    return db.select()
      .from(landingPages)
      .where(eq(landingPages.courseId, courseId))
      .orderBy(desc(landingPages.createdAt));
  }
  
  async getLandingPagesByUserId(userId: number): Promise<LandingPage[]> {
    return db.select()
      .from(landingPages)
      .where(eq(landingPages.userId, userId))
      .orderBy(desc(landingPages.createdAt));
  }
  
  async createLandingPage(landingPage: InsertLandingPage): Promise<LandingPage> {
    const [newLandingPage] = await db.insert(landingPages)
      .values({
        ...landingPage,
        visits: 0,
        conversions: 0,
        conversionRate: 0,
        isPublished: false,
        publishedAt: null,
        createdAt: new Date(),
        updatedAt: new Date()
      })
      .returning();
    
    return newLandingPage;
  }
  
  async updateLandingPage(id: number, landingPage: Partial<LandingPage>): Promise<LandingPage | undefined> {
    const [updatedLandingPage] = await db.update(landingPages)
      .set({
        ...landingPage,
        updatedAt: new Date()
      })
      .where(eq(landingPages.id, id))
      .returning();
    
    return updatedLandingPage;
  }
  
  async publishLandingPage(id: number): Promise<LandingPage | undefined> {
    const now = new Date();
    
    const [publishedLandingPage] = await db.update(landingPages)
      .set({
        status: "published",
        publishedAt: now,
        updatedAt: now
      })
      .where(eq(landingPages.id, id))
      .returning();
    
    return publishedLandingPage;
  }
  
  async deleteLandingPage(id: number): Promise<boolean> {
    // First delete all visits for this landing page
    await db.delete(landingPageVisits)
      .where(eq(landingPageVisits.landingPageId, id));
    
    // Then delete the landing page
    const result = await db.delete(landingPages)
      .where(eq(landingPages.id, id))
      .returning();
    
    return result.length > 0;
  }
  
  // Landing Page Visit operations
  async recordLandingPageVisit(visit: InsertLandingPageVisit): Promise<LandingPageVisit> {
    // Make sure all fields are properly defined and have appropriate data types
    const visitData = {
      landingPageId: visit.landingPageId,
      visitorIp: visit.visitorIp || null,
      userAgent: visit.userAgent || null,
      referrer: visit.referrer || null,
      utmSource: visit.utmSource || null,
      utmMedium: visit.utmMedium || null,
      utmCampaign: visit.utmCampaign || null,
      utmContent: visit.utmContent || null,
      utmTerm: visit.utmTerm || null,
      converted: false,
      convertedAt: null,
      timestamp: new Date()
    };

    const [newVisit] = await db.insert(landingPageVisits)
      .values(visitData)
      .returning();
    
    // Update landing page stats
    await this.updateLandingPageStats(visit.landingPageId);
    
    return newVisit;
  }
  
  async getLandingPageVisits(landingPageId: number): Promise<LandingPageVisit[]> {
    return db.select()
      .from(landingPageVisits)
      .where(eq(landingPageVisits.landingPageId, landingPageId))
      .orderBy(desc(landingPageVisits.timestamp));
  }
  
  async recordLandingPageConversion(id: number): Promise<LandingPageVisit | undefined> {
    const [visit] = await db.select()
      .from(landingPageVisits)
      .where(eq(landingPageVisits.id, id));
    
    if (!visit) {
      return undefined;
    }
    
    const now = new Date();
    const [updatedVisit] = await db.update(landingPageVisits)
      .set({
        converted: true,
        timestamp: now // Use timestamp for conversion date as convertedAt doesn't exist in schema
      })
      .where(eq(landingPageVisits.id, id))
      .returning();
    
    // Update landing page stats
    await this.updateLandingPageStats(visit.landingPageId);
    
    return updatedVisit;
  }
  
  async updateLandingPageStats(landingPageId: number): Promise<LandingPage | undefined> {
    // Get the landing page
    const [landingPage] = await db.select()
      .from(landingPages)
      .where(eq(landingPages.id, landingPageId));
    
    if (!landingPage) {
      return undefined;
    }
    
    // Get the visits
    const visits = await db.select()
      .from(landingPageVisits)
      .where(eq(landingPageVisits.landingPageId, landingPageId));
    
    // Calculate stats
    const totalVisits = visits.length;
    const totalConversions = visits.filter(v => v.converted).length;
    const conversionRate = totalVisits > 0 ? (totalConversions / totalVisits) * 100 : 0;
    
    // Update landing page stats
    const [updatedLandingPage] = await db.update(landingPages)
      .set({
        viewCount: totalVisits,
        uniqueVisitorCount: totalVisits, // Simplification, in reality we'd count unique IPs
        conversionCount: totalConversions,
        conversionRate: Math.round(conversionRate * 100) / 100 + "", // Convert to string and round to 2 decimal places
        updatedAt: new Date()
      })
      .where(eq(landingPages.id, landingPageId))
      .returning();
    
    return updatedLandingPage;
  }

  // Micro-learning methods
  async getMicroLearningSegment(id: number): Promise<MicroLearningSegment | undefined> {
    const [segment] = await db.select().from(microLearningSegments).where(eq(microLearningSegments.id, id));
    return segment;
  }

  async getMicroLearningSegmentsByLessonId(lessonId: number): Promise<MicroLearningSegment[]> {
    return await db.select().from(microLearningSegments).where(eq(microLearningSegments.lessonId, lessonId));
  }

  async createMicroLearningSegment(segment: InsertMicroLearningSegment): Promise<MicroLearningSegment> {
    const [newSegment] = await db.insert(microLearningSegments).values(segment).returning();
    return newSegment;
  }

  async updateMicroLearningSegment(id: number, segment: Partial<MicroLearningSegment>): Promise<MicroLearningSegment | undefined> {
    const [updatedSegment] = await db
      .update(microLearningSegments)
      .set(segment)
      .where(eq(microLearningSegments.id, id))
      .returning();
    return updatedSegment;
  }

  async deleteMicroLearningSegment(id: number): Promise<boolean> {
    await db.delete(microLearningSegments).where(eq(microLearningSegments.id, id));
    return true;
  }

  async getMicroLearningKnowledgeCheck(id: number): Promise<MicroLearningKnowledgeCheck | undefined> {
    const [check] = await db.select().from(microLearningKnowledgeChecks).where(eq(microLearningKnowledgeChecks.id, id));
    return check;
  }

  async getMicroLearningKnowledgeChecksByLessonId(lessonId: number): Promise<MicroLearningKnowledgeCheck[]> {
    return await db.select().from(microLearningKnowledgeChecks).where(eq(microLearningKnowledgeChecks.lessonId, lessonId));
  }

  async createMicroLearningKnowledgeCheck(check: InsertMicroLearningKnowledgeCheck): Promise<MicroLearningKnowledgeCheck> {
    const [newCheck] = await db.insert(microLearningKnowledgeChecks).values(check).returning();
    return newCheck;
  }

  async updateMicroLearningKnowledgeCheck(id: number, check: Partial<MicroLearningKnowledgeCheck>): Promise<MicroLearningKnowledgeCheck | undefined> {
    const [updatedCheck] = await db
      .update(microLearningKnowledgeChecks)
      .set(check)
      .where(eq(microLearningKnowledgeChecks.id, id))
      .returning();
    return updatedCheck;
  }

  async deleteMicroLearningKnowledgeCheck(id: number): Promise<boolean> {
    await db.delete(microLearningKnowledgeChecks).where(eq(microLearningKnowledgeChecks.id, id));
    return true;
  }

  async getMicroLearningUserProgress(id: number): Promise<MicroLearningUserProgress | undefined> {
    const [progress] = await db.select().from(microLearningUserProgress).where(eq(microLearningUserProgress.id, id));
    return progress;
  }

  async getMicroLearningUserProgressByUserAndLesson(userId: number, lessonId: number): Promise<MicroLearningUserProgress | undefined> {
    const [progress] = await db
      .select()
      .from(microLearningUserProgress)
      .where(
        and(
          eq(microLearningUserProgress.userId, userId),
          eq(microLearningUserProgress.lessonId, lessonId)
        )
      );
    return progress;
  }

  async createMicroLearningUserProgress(progress: InsertMicroLearningUserProgress): Promise<MicroLearningUserProgress> {
    const [newProgress] = await db.insert(microLearningUserProgress).values(progress).returning();
    return newProgress;
  }

  async updateMicroLearningUserProgress(id: number, progress: Partial<MicroLearningUserProgress>): Promise<MicroLearningUserProgress | undefined> {
    const [updatedProgress] = await db
      .update(microLearningUserProgress)
      .set(progress)
      .where(eq(microLearningUserProgress.id, id))
      .returning();
    return updatedProgress;
  }

  async getUserKnowledgeCheckResponse(id: number): Promise<UserKnowledgeCheckResponse | undefined> {
    const [response] = await db.select().from(userKnowledgeCheckResponses).where(eq(userKnowledgeCheckResponses.id, id));
    return response;
  }

  async getUserKnowledgeCheckResponsesByLessonId(userId: number, lessonId: number): Promise<UserKnowledgeCheckResponse[]> {
    // First get all knowledge checks for this lesson
    const knowledgeChecks = await this.getMicroLearningKnowledgeChecksByLessonId(lessonId);
    
    if (knowledgeChecks.length === 0) {
      return [];
    }
    
    // Get all responses for these knowledge checks
    const knowledgeCheckIds = knowledgeChecks.map(check => check.id);
    
    return await db
      .select()
      .from(userKnowledgeCheckResponses)
      .where(
        and(
          eq(userKnowledgeCheckResponses.userId, userId),
          inArray(userKnowledgeCheckResponses.knowledgeCheckId, knowledgeCheckIds)
        )
      );
  }

  async createUserKnowledgeCheckResponse(response: InsertUserKnowledgeCheckResponse): Promise<UserKnowledgeCheckResponse> {
    const [newResponse] = await db.insert(userKnowledgeCheckResponses).values(response).returning();
    return newResponse;
  }

  // Course Draft operations for real-time auto-save
  async getCourseDraft(userId: number, draftId: string): Promise<CourseDraft | undefined> {
    const [draft] = await db.select()
      .from(courseDrafts)
      .where(and(eq(courseDrafts.userId, userId), eq(courseDrafts.draftId, draftId)));
    return draft;
  }

  async getUserCourseDrafts(userId: number): Promise<CourseDraft[]> {
    return await db.select()
      .from(courseDrafts)
      .where(eq(courseDrafts.userId, userId))
      .orderBy(desc(courseDrafts.updatedAt));
  }

  async saveCourseDraft(draft: InsertCourseDraft): Promise<CourseDraft> {
    const [savedDraft] = await db.insert(courseDrafts)
      .values({
        ...draft,
        lastAutoSave: new Date(),
      })
      .onConflictDoUpdate({
        target: [courseDrafts.userId, courseDrafts.draftId],
        set: {
          courseDetails: draft.courseDetails,
          courseStructure: draft.courseStructure,
          courseScripts: draft.courseScripts,
          mediaAttachments: draft.mediaAttachments,
          quizData: draft.quizData,
          publishData: draft.publishData,
          stepProgress: draft.stepProgress,
          completedSteps: draft.completedSteps,
          generatedAudioFiles: draft.generatedAudioFiles,
          lastAutoSave: new Date(),
          updatedAt: new Date(),
        },
      })
      .returning();
    return savedDraft;
  }

  async updateCourseDraft(userId: number, draftId: string, updates: Partial<CourseDraft>): Promise<CourseDraft | undefined> {
    const [updatedDraft] = await db.update(courseDrafts)
      .set({
        ...updates,
        lastAutoSave: new Date(),
        updatedAt: new Date(),
      })
      .where(and(eq(courseDrafts.userId, userId), eq(courseDrafts.draftId, draftId)))
      .returning();
    return updatedDraft;
  }

  async deleteCourseDraft(userId: number, draftId: string): Promise<boolean> {
    const result = await db.delete(courseDrafts)
      .where(and(eq(courseDrafts.userId, userId), eq(courseDrafts.draftId, draftId)));
    return result.rowCount > 0;
  }

  async cleanupOldCourseDrafts(userId: number, keepCount: number = 10): Promise<number> {
    // Get all drafts for user, ordered by last update
    const userDrafts = await this.getUserCourseDrafts(userId);
    
    if (userDrafts.length <= keepCount) {
      return 0;
    }

    // Get IDs of drafts to delete (oldest ones)
    const draftsToDelete = userDrafts.slice(keepCount);
    const draftIds = draftsToDelete.map(draft => draft.draftId);

    let deletedCount = 0;
    for (const draftId of draftIds) {
      if (await this.deleteCourseDraft(userId, draftId)) {
        deletedCount++;
      }
    }

    return deletedCount;
  }

  // AI Credits and Video Generation Methods
  async recordCreditTransaction(transaction: {
    userId: number;
    amount: number;
    type: 'debit' | 'credit';
    description: string;
    relatedJobId?: string;
  }): Promise<void> {
    if (!db) throw new Error('Database not initialized');

    try {
      // For now, just log the transaction - would need proper table implementation
      console.log('Credit transaction recorded:', transaction);
    } catch (error) {
      console.error('Error recording credit transaction:', error);
      throw error;
    }
  }

  async createVideoPending(videoJob: {
    jobId: string;
    userId: number;
    status: string;
    lessonId?: string;
    moduleId?: string;
    progress?: number;
  }): Promise<void> {
    if (!db) throw new Error('Database not initialized');

    try {
      // For now, just log the video job - would need proper table implementation
      console.log('Video job created:', videoJob);
    } catch (error) {
      console.error('Error creating video job:', error);
      throw error;
    }
  }

  async updateVideoPending(jobId: string, updates: {
    status?: string;
    progress?: number;
    videoUrl?: string;
    audioUrl?: string;
    error?: string;
    completedAt?: string;
  }): Promise<void> {
    if (!db) throw new Error('Database not initialized');

    try {
      // For now, just log the update - would need proper table implementation
      console.log('Video job updated:', { jobId, updates });
    } catch (error) {
      console.error('Error updating video job:', error);
      throw error;
    }
  }

  // Additional missing methods for compatibility
  async getAllUsers(): Promise<User[]> {
    if (!db) throw new Error('Database not initialized');
    try {
      const result = await db.execute(sql`SELECT * FROM users`);
      return result.rows as User[];
    } catch (error) {
      console.error('Error getting all users:', error);
      return [];
    }
  }

  async updateUserAiCredits(userId: number, credits: number): Promise<void> {
    if (!db) throw new Error('Database not initialized');
    try {
      await db.execute(sql`UPDATE users SET ai_credits = ${credits} WHERE id = ${userId}`);
    } catch (error) {
      console.error('Error updating user AI credits:', error);
      throw error;
    }
  }

  async addUserCredits(userId: number, amount: number): Promise<void> {
    if (!db) throw new Error('Database not initialized');
    try {
      await db.execute(sql`UPDATE users SET ai_credits = COALESCE(ai_credits, 0) + ${amount} WHERE id = ${userId}`);
    } catch (error) {
      console.error('Error adding user credits:', error);
      throw error;
    }
  }

  async updateUserStripeId(userId: number, customerId: string): Promise<void> {
    if (!db) throw new Error('Database not initialized');
    try {
      await db.execute(sql`UPDATE users SET stripe_customer_id = ${customerId} WHERE id = ${userId}`);
    } catch (error) {
      console.error('Error updating user Stripe ID:', error);
      throw error;
    }
  }

  async getCreditPurchaseHistory(userId: number): Promise<any[]> {
    if (!db) throw new Error('Database not initialized');
    try {
      // For now, return empty array - would need proper table implementation
      return [];
    } catch (error) {
      console.error('Error getting credit purchase history:', error);
      return [];
    }
  }

  // Quiz-related methods
  async createQuiz(quiz: any): Promise<any> {
    if (!db) throw new Error('Database not initialized');
    try {
      console.log('Quiz created:', quiz);
      return { id: Date.now(), ...quiz };
    } catch (error) {
      console.error('Error creating quiz:', error);
      throw error;
    }
  }

  async getQuiz(id: number): Promise<any> {
    if (!db) throw new Error('Database not initialized');
    try {
      console.log('Getting quiz:', id);
      return null;
    } catch (error) {
      console.error('Error getting quiz:', error);
      return null;
    }
  }

  async getQuizzesByCourseId(courseId: number): Promise<any[]> {
    if (!db) throw new Error('Database not initialized');
    try {
      console.log('Getting quizzes for course:', courseId);
      return [];
    } catch (error) {
      console.error('Error getting quizzes by course ID:', error);
      return [];
    }
  }

  async getQuizzesByModuleId(moduleId: number): Promise<any[]> {
    if (!db) throw new Error('Database not initialized');
    try {
      console.log('Getting quizzes for module:', moduleId);
      return [];
    } catch (error) {
      console.error('Error getting quizzes by module ID:', error);
      return [];
    }
  }

  async updateQuiz(id: number, updates: any): Promise<any> {
    if (!db) throw new Error('Database not initialized');
    try {
      console.log('Updating quiz:', id, updates);
      return { id, ...updates };
    } catch (error) {
      console.error('Error updating quiz:', error);
      throw error;
    }
  }

  async createQuizQuestion(question: any): Promise<any> {
    if (!db) throw new Error('Database not initialized');
    try {
      console.log('Quiz question created:', question);
      return { id: Date.now(), ...question };
    } catch (error) {
      console.error('Error creating quiz question:', error);
      throw error;
    }
  }

  async getQuizQuestions(quizId: number): Promise<any[]> {
    if (!db) throw new Error('Database not initialized');
    try {
      console.log('Getting quiz questions:', quizId);
      return [];
    } catch (error) {
      console.error('Error getting quiz questions:', error);
      return [];
    }
  }

  async createQuizAttempt(attempt: any): Promise<any> {
    if (!db) throw new Error('Database not initialized');
    try {
      console.log('Quiz attempt created:', attempt);
      return { id: Date.now(), ...attempt };
    } catch (error) {
      console.error('Error creating quiz attempt:', error);
      throw error;
    }
  }

  async getQuizAttempts(quizId: number, userId: number): Promise<any[]> {
    if (!db) throw new Error('Database not initialized');
    try {
      console.log('Getting quiz attempts:', quizId, userId);
      return [];
    } catch (error) {
      console.error('Error getting quiz attempts:', error);
      return [];
    }
  }

  // Micro-learning operations
  async getMicroLearningSegment(id: number): Promise<MicroLearningSegment | undefined> {
    if (!db) throw new Error('Database not initialized');
    try {
      const result = await db.select()
        .from(microLearningSegments)
        .where(eq(microLearningSegments.id, id));
      return result[0] as MicroLearningSegment;
    } catch (error) {
      console.error('Error getting micro-learning segment:', error);
      return undefined;
    }
  }

  async getMicroLearningSegmentsByLessonId(lessonId: number): Promise<MicroLearningSegment[]> {
    if (!db) throw new Error('Database not initialized');
    try {
      const result = await db.select()
        .from(microLearningSegments)
        .where(eq(microLearningSegments.lessonId, lessonId));
      return result as MicroLearningSegment[];
    } catch (error) {
      console.error('Error getting micro-learning segments:', error);
      return [];
    }
  }

  async createMicroLearningSegment(segment: InsertMicroLearningSegment): Promise<MicroLearningSegment> {
    if (!db) throw new Error('Database not initialized');
    try {
      const result = await db.insert(microLearningSegments)
        .values(segment)
        .returning();
      return result[0] as MicroLearningSegment;
    } catch (error) {
      console.error('Error creating micro-learning segment:', error);
      throw error;
    }
  }

  async updateMicroLearningSegment(id: number, segment: Partial<MicroLearningSegment>): Promise<MicroLearningSegment | undefined> {
    if (!db) throw new Error('Database not initialized');
    try {
      const result = await db.update(microLearningSegments)
        .set(segment)
        .where(eq(microLearningSegments.id, id))
        .returning();
      return result[0] as MicroLearningSegment;
    } catch (error) {
      console.error('Error updating micro-learning segment:', error);
      return undefined;
    }
  }

  async deleteMicroLearningSegment(id: number): Promise<boolean> {
    if (!db) throw new Error('Database not initialized');
    try {
      const result = await db.delete(microLearningSegments)
        .where(eq(microLearningSegments.id, id));
      return result.rowCount > 0;
    } catch (error) {
      console.error('Error deleting micro-learning segment:', error);
      return false;
    }
  }

  async getMicroLearningKnowledgeCheck(id: number): Promise<MicroLearningKnowledgeCheck | undefined> {
    if (!db) throw new Error('Database not initialized');
    try {
      const result = await db.select()
        .from(microLearningKnowledgeChecks)
        .where(eq(microLearningKnowledgeChecks.id, id));
      return result[0] as MicroLearningKnowledgeCheck;
    } catch (error) {
      console.error('Error getting micro-learning knowledge check:', error);
      return undefined;
    }
  }

  async getMicroLearningKnowledgeChecksByLessonId(lessonId: number): Promise<MicroLearningKnowledgeCheck[]> {
    if (!db) throw new Error('Database not initialized');
    try {
      const result = await db.select()
        .from(microLearningKnowledgeChecks)
        .where(eq(microLearningKnowledgeChecks.lessonId, lessonId));
      return result as MicroLearningKnowledgeCheck[];
    } catch (error) {
      console.error('Error getting micro-learning knowledge checks:', error);
      return [];
    }
  }

  async createMicroLearningKnowledgeCheck(check: InsertMicroLearningKnowledgeCheck): Promise<MicroLearningKnowledgeCheck> {
    if (!db) throw new Error('Database not initialized');
    try {
      const result = await db.insert(microLearningKnowledgeChecks)
        .values(check)
        .returning();
      return result[0] as MicroLearningKnowledgeCheck;
    } catch (error) {
      console.error('Error creating micro-learning knowledge check:', error);
      throw error;
    }
  }

  async updateMicroLearningKnowledgeCheck(id: number, check: Partial<MicroLearningKnowledgeCheck>): Promise<MicroLearningKnowledgeCheck | undefined> {
    if (!db) throw new Error('Database not initialized');
    try {
      const result = await db.update(microLearningKnowledgeChecks)
        .set(check)
        .where(eq(microLearningKnowledgeChecks.id, id))
        .returning();
      return result[0] as MicroLearningKnowledgeCheck;
    } catch (error) {
      console.error('Error updating micro-learning knowledge check:', error);
      return undefined;
    }
  }

  async deleteMicroLearningKnowledgeCheck(id: number): Promise<boolean> {
    if (!db) throw new Error('Database not initialized');
    try {
      const result = await db.delete(microLearningKnowledgeChecks)
        .where(eq(microLearningKnowledgeChecks.id, id));
      return result.rowCount > 0;
    } catch (error) {
      console.error('Error deleting micro-learning knowledge check:', error);
      return false;
    }
  }

  async getMicroLearningUserProgress(id: number): Promise<MicroLearningUserProgress | undefined> {
    if (!db) throw new Error('Database not initialized');
    try {
      const result = await db.select()
        .from(microLearningUserProgress)
        .where(eq(microLearningUserProgress.id, id));
      return result[0] as MicroLearningUserProgress;
    } catch (error) {
      console.error('Error getting micro-learning user progress:', error);
      return undefined;
    }
  }

  async getMicroLearningUserProgressByUserAndLesson(userId: number, lessonId: number): Promise<MicroLearningUserProgress | undefined> {
    if (!db) throw new Error('Database not initialized');
    try {
      const result = await db.select()
        .from(microLearningUserProgress)
        .where(and(
          eq(microLearningUserProgress.userId, userId),
          eq(microLearningUserProgress.lessonId, lessonId)
        ));
      return result[0] as MicroLearningUserProgress;
    } catch (error) {
      console.error('Error getting micro-learning user progress:', error);
      return undefined;
    }
  }

  async createMicroLearningUserProgress(progress: InsertMicroLearningUserProgress): Promise<MicroLearningUserProgress> {
    if (!db) throw new Error('Database not initialized');
    try {
      const result = await db.insert(microLearningUserProgress)
        .values(progress)
        .returning();
      return result[0] as MicroLearningUserProgress;
    } catch (error) {
      console.error('Error creating micro-learning user progress:', error);
      throw error;
    }
  }

  async updateMicroLearningUserProgress(id: number, progress: Partial<MicroLearningUserProgress>): Promise<MicroLearningUserProgress | undefined> {
    if (!db) throw new Error('Database not initialized');
    try {
      const result = await db.update(microLearningUserProgress)
        .set(progress)
        .where(eq(microLearningUserProgress.id, id))
        .returning();
      return result[0] as MicroLearningUserProgress;
    } catch (error) {
      console.error('Error updating micro-learning user progress:', error);
      return undefined;
    }
  }

  async getUserKnowledgeCheckResponse(id: number): Promise<UserKnowledgeCheckResponse | undefined> {
    if (!db) throw new Error('Database not initialized');
    try {
      const result = await db.select()
        .from(userKnowledgeCheckResponses)
        .where(eq(userKnowledgeCheckResponses.id, id));
      return result[0] as UserKnowledgeCheckResponse;
    } catch (error) {
      console.error('Error getting user knowledge check response:', error);
      return undefined;
    }
  }

  async getUserKnowledgeCheckResponsesByLessonId(userId: number, lessonId: number): Promise<UserKnowledgeCheckResponse[]> {
    if (!db) throw new Error('Database not initialized');
    try {
      const result = await db.select()
        .from(userKnowledgeCheckResponses)
        .where(and(
          eq(userKnowledgeCheckResponses.userId, userId),
          eq(userKnowledgeCheckResponses.lessonId, lessonId)
        ));
      return result as UserKnowledgeCheckResponse[];
    } catch (error) {
      console.error('Error getting user knowledge check responses:', error);
      return [];
    }
  }

  async createUserKnowledgeCheckResponse(response: InsertUserKnowledgeCheckResponse): Promise<UserKnowledgeCheckResponse> {
    if (!db) throw new Error('Database not initialized');
    try {
      const result = await db.insert(userKnowledgeCheckResponses)
        .values(response)
        .returning();
      return result[0] as UserKnowledgeCheckResponse;
    } catch (error) {
      console.error('Error creating user knowledge check response:', error);
      throw error;
    }
  }

  // Template operations
  async getAllTemplates(): Promise<Template[]> {
    if (!db) throw new Error('Database not initialized');
    try {
      const result = await db.select().from(templates);
      return result as Template[];
    } catch (error) {
      console.error('Error getting all templates:', error);
      return [];
    }
  }

  async getTemplate(id: number): Promise<Template | undefined> {
    if (!db) throw new Error('Database not initialized');
    try {
      const result = await db.select()
        .from(templates)
        .where(eq(templates.id, id));
      return result[0] as Template;
    } catch (error) {
      console.error('Error getting template:', error);
      return undefined;
    }
  }

  // Template History operations
  async getTemplateHistory(id: number): Promise<TemplateHistory | undefined> {
    if (!db) throw new Error('Database not initialized');
    try {
      const result = await db.select()
        .from(templateHistory)
        .where(eq(templateHistory.id, id));
      return result[0] as TemplateHistory;
    } catch (error) {
      console.error('Error getting template history:', error);
      return undefined;
    }
  }

  async getTemplateHistoryByUserId(userId: number): Promise<TemplateHistory[]> {
    if (!db) throw new Error('Database not initialized');
    try {
      const result = await db.select()
        .from(templateHistory)
        .where(eq(templateHistory.userId, userId));
      return result as TemplateHistory[];
    } catch (error) {
      console.error('Error getting template history by user:', error);
      return [];
    }
  }

  async createTemplateHistory(history: InsertTemplateHistory): Promise<TemplateHistory> {
    if (!db) throw new Error('Database not initialized');
    try {
      const result = await db.insert(templateHistory)
        .values(history)
        .returning();
      return result[0] as TemplateHistory;
    } catch (error) {
      console.error('Error creating template history:', error);
      throw error;
    }
  }

  async updateTemplateHistoryFavorite(id: number, favorited: boolean): Promise<TemplateHistory | undefined> {
    if (!db) throw new Error('Database not initialized');
    try {
      const result = await db.update(templateHistory)
        .set({ favorited })
        .where(eq(templateHistory.id, id))
        .returning();
      return result[0] as TemplateHistory;
    } catch (error) {
      console.error('Error updating template history favorite:', error);
      return undefined;
    }
  }

  // Analytics Events operations
  async recordAnalyticsEvent(event: InsertAnalyticsEvent): Promise<AnalyticsEvent> {
    if (!db) throw new Error('Database not initialized');
    try {
      const result = await db.insert(analyticsEvents)
        .values(event)
        .returning();
      return result[0] as AnalyticsEvent;
    } catch (error) {
      console.error('Error recording analytics event:', error);
      throw error;
    }
  }

  async getAnalyticsEventsByCourseId(courseId: number, limit?: number): Promise<AnalyticsEvent[]> {
    if (!db) throw new Error('Database not initialized');
    try {
      let query = db.select()
        .from(analyticsEvents)
        .where(eq(analyticsEvents.courseId, courseId));

      if (limit) {
        query = query.limit(limit);
      }

      const result = await query;
      return result as AnalyticsEvent[];
    } catch (error) {
      console.error('Error getting analytics events by course:', error);
      return [];
    }
  }

  async getAnalyticsEventsByUserId(userId: number, limit?: number): Promise<AnalyticsEvent[]> {
    if (!db) throw new Error('Database not initialized');
    try {
      let query = db.select()
        .from(analyticsEvents)
        .where(eq(analyticsEvents.userId, userId));

      if (limit) {
        query = query.limit(limit);
      }

      const result = await query;
      return result as AnalyticsEvent[];
    } catch (error) {
      console.error('Error getting analytics events by user:', error);
      return [];
    }
  }

  async getAnalyticsEventsByLessonId(lessonId: number, limit?: number): Promise<AnalyticsEvent[]> {
    if (!db) throw new Error('Database not initialized');
    try {
      let query = db.select()
        .from(analyticsEvents)
        .where(eq(analyticsEvents.lessonId, lessonId));

      if (limit) {
        query = query.limit(limit);
      }

      const result = await query;
      return result as AnalyticsEvent[];
    } catch (error) {
      console.error('Error getting analytics events by lesson:', error);
      return [];
    }
  }

  // Dashboard Analytics
  async getCoursePerformanceOverview(courseId: number): Promise<any> {
    if (!db) throw new Error('Database not initialized');
    try {
      // Basic implementation - would need more complex queries in production
      const analytics = await db.select()
        .from(courseAnalytics)
        .where(eq(courseAnalytics.courseId, courseId));
      return analytics[0] || {};
    } catch (error) {
      console.error('Error getting course performance overview:', error);
      return {};
    }
  }

  async getUserEngagementStats(userId: number): Promise<any> {
    if (!db) throw new Error('Database not initialized');
    try {
      // Basic implementation - would need more complex queries in production
      const events = await db.select()
        .from(analyticsEvents)
        .where(eq(analyticsEvents.userId, userId))
        .limit(100);
      return { totalEvents: events.length, recentEvents: events };
    } catch (error) {
      console.error('Error getting user engagement stats:', error);
      return {};
    }
  }

  async getPopularCourses(limit?: number): Promise<any[]> {
    if (!db) throw new Error('Database not initialized');
    try {
      let query = db.select().from(courses);

      if (limit) {
        query = query.limit(limit);
      }

      const result = await query;
      return result;
    } catch (error) {
      console.error('Error getting popular courses:', error);
      return [];
    }
  }

  // AI Generated Images operations
  async getAiGeneratedImage(id: number): Promise<AiGeneratedImage | undefined> {
    if (!db) throw new Error('Database not initialized');
    try {
      const result = await db.select()
        .from(aiGeneratedImages)
        .where(eq(aiGeneratedImages.id, id));
      return result[0] as AiGeneratedImage;
    } catch (error) {
      console.error('Error getting AI generated image:', error);
      return undefined;
    }
  }

  async getAiGeneratedImagesByUserId(userId: number): Promise<AiGeneratedImage[]> {
    if (!db) throw new Error('Database not initialized');
    try {
      const result = await db.select()
        .from(aiGeneratedImages)
        .where(eq(aiGeneratedImages.userId, userId));
      return result as AiGeneratedImage[];
    } catch (error) {
      console.error('Error getting AI generated images by user:', error);
      return [];
    }
  }

  async createAiGeneratedImage(image: InsertAiGeneratedImage): Promise<AiGeneratedImage> {
    if (!db) throw new Error('Database not initialized');
    try {
      const result = await db.insert(aiGeneratedImages)
        .values(image)
        .returning();
      return result[0] as AiGeneratedImage;
    } catch (error) {
      console.error('Error creating AI generated image:', error);
      throw error;
    }
  }

  async deleteAiGeneratedImage(id: number): Promise<boolean> {
    if (!db) throw new Error('Database not initialized');
    try {
      const result = await db.delete(aiGeneratedImages)
        .where(eq(aiGeneratedImages.id, id));
      return result.rowCount > 0;
    } catch (error) {
      console.error('Error deleting AI generated image:', error);
      return false;
    }
  }
}

export const storage = new DatabaseStorage();
