/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as ElevenLabs from "../index";
export interface VoiceVerificationResponse {
    /** Whether the voice requires verification. */
    requires_verification: boolean;
    /** Whether the voice has been verified. */
    is_verified: boolean;
    /** List of verification failures. */
    verification_failures: string[];
    /** The number of verification attempts. */
    verification_attempts_count: number;
    /** The language of the voice. */
    language?: string;
    /** Number of times a verification was attempted. */
    verification_attempts?: ElevenLabs.VerificationAttemptResponse[];
}
