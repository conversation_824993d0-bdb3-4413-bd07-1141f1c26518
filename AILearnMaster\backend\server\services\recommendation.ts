import { storage } from "../storage";

export interface Course {
  id: number;
  title: string;
  description: string;
  category: string;
  thumbnail?: string;
  rating?: number;
  enrollmentCount?: number;
  level?: string;
  tags?: string[];
  createdAt?: string;
  updatedAt?: string;
}

export interface AIInsight {
  type: 'trending' | 'recommended' | 'personalized' | 'similar';
  message: string;
  confidence: number;
}

export interface RecommendationGroup {
  title: string;
  insights: AIInsight[];
  courses: Course[];
}

/**
 * Generate personalized recommendations with AI insights for a user
 */
export async function generateRecommendations(userId: number): Promise<RecommendationGroup[]> {
  try {
    // Get user's courses
    const userCourses = await storage.getUserCourses(userId);
    
    // Get all available courses
    const allCourses = await storage.getAllCourses();
    
    // Find courses the user hasn't taken yet
    const unseenCourses = allCourses.filter(course => 
      !userCourses.some(uc => uc.id === course.id)
    );
    
    // Use categories from user's courses to find similar courses
    const userCategories = userCourses.reduce((categories, course) => {
      if (course.category && !categories.includes(course.category)) {
        categories.push(course.category);
      }
      return categories;
    }, [] as string[]);
    
    // Generate recommendations using AI insights if available
    const recommendations = await generatePersonalizedAIRecommendations(
      userCourses,
      unseenCourses,
      userCategories
    );
    
    return recommendations;
  } catch (error) {
    console.error("Error generating recommendations:", error);
    // Fallback to basic recommendations
    return generateBasicRecommendations();
  }
}

/**
 * Use OpenAI to generate personalized course recommendations
 */
async function generatePersonalizedAIRecommendations(
  userCourses: Course[],
  availableCourses: Course[],
  userCategories: string[]
): Promise<RecommendationGroup[]> {
  // Only proceed with AI recommendations if we have user history
  if (userCourses.length === 0 || availableCourses.length === 0) {
    return generateBasicRecommendations();
  }
  
  try {
    // First group: Trending in Your Categories
    const trendingCourses = availableCourses
      .filter(course => userCategories.includes(course.category))
      .sort((a, b) => (b.enrollmentCount || 0) - (a.enrollmentCount || 0))
      .slice(0, 5);
    
    // Second group: Recommended For You
    const recommendedCourses = availableCourses
      .filter(course => !trendingCourses.some(tc => tc.id === course.id))
      .sort((a, b) => (b.rating || 0) - (a.rating || 0))
      .slice(0, 5);
    
    // Third group: Similar to What You've Completed
    const similarCourses = findSimilarCourses(userCourses, availableCourses, trendingCourses, recommendedCourses);
    
    // Generate AI insights for each recommendation
    const trendingInsights = trendingCourses.map(course => generateInsightForCourse(course, 'trending'));
    const recommendedInsights = recommendedCourses.map(course => generateInsightForCourse(course, 'recommended'));
    const similarInsights = similarCourses.map(course => generateInsightForCourse(course, 'similar'));
    
    return [
      {
        title: "Trending in Your Categories",
        insights: trendingInsights,
        courses: trendingCourses
      },
      {
        title: "Recommended For You",
        insights: recommendedInsights,
        courses: recommendedCourses
      },
      {
        title: "Similar to Your Courses",
        insights: similarInsights,
        courses: similarCourses
      }
    ];
  } catch (error) {
    console.error("Error generating AI recommendations:", error);
    return generateBasicRecommendations();
  }
}

/**
 * Find similar courses based on user's completed courses
 */
function findSimilarCourses(
  userCourses: Course[],
  availableCourses: Course[],
  excludeCourses1: Course[],
  excludeCourses2: Course[]
): Course[] {
  // Filter out courses that are already in other recommendation groups
  const eligibleCourses = availableCourses.filter(
    course => !excludeCourses1.some(ec => ec.id === course.id) && 
              !excludeCourses2.some(ec => ec.id === course.id)
  );
  
  // Simple similarity algorithm based on categories and tags
  const similarityCourses = eligibleCourses.map(course => {
    let score = 0;
    
    // Check category matches
    userCourses.forEach(userCourse => {
      if (userCourse.category === course.category) {
        score += 2;
      }
      
      // Check tag matches
      if (userCourse.tags && course.tags) {
        const commonTags = userCourse.tags.filter(tag => course.tags?.includes(tag));
        score += commonTags.length;
      }
    });
    
    return { course, score };
  });
  
  // Sort by similarity score and return top 5
  return similarityCourses
    .sort((a, b) => b.score - a.score)
    .slice(0, 5)
    .map(item => item.course);
}

/**
 * Generate insightful message about why a course is recommended
 */
function generateInsightForCourse(course: Course, type: AIInsight['type']): AIInsight {
  // Generate some basic insights based on course properties
  let message = '';
  let confidence = 0.7;
  
  switch (type) {
    case 'trending':
      message = `${course.enrollmentCount || 100}+ students enrolled in the past month`;
      confidence = 0.85;
      break;
    case 'recommended':
      message = course.rating 
        ? `Highly rated (${course.rating}/5) by other learners` 
        : `A popular choice for ${course.category} learning`;
      confidence = course.rating ? 0.9 : 0.75;
      break;
    case 'similar':
      message = `Content similar to courses you've enjoyed in ${course.category}`;
      confidence = 0.8;
      break;
    case 'personalized':
      message = `Matched to your learning preferences`;
      confidence = 0.7;
      break;
  }
  
  return {
    type,
    message,
    confidence
  };
}

/**
 * Generate basic recommendations when no user data is available
 */
function generateBasicRecommendations(): RecommendationGroup[] {
  return [
    {
      title: "Popular Courses",
      insights: [
        {
          type: 'trending',
          message: 'Highly popular on our platform',
          confidence: 0.85
        }
      ],
      courses: getMockCourses().slice(0, 5)
    },
    {
      title: "New Releases",
      insights: [
        {
          type: 'recommended',
          message: 'Fresh content just added',
          confidence: 0.75
        }
      ],
      courses: getMockCourses().slice(5, 10)
    },
    {
      title: "Editor's Choice",
      insights: [
        {
          type: 'personalized',
          message: 'Specially curated by our team',
          confidence: 0.9
        }
      ],
      courses: getMockCourses().slice(10, 15)
    }
  ];
}

/**
 * Generate mock courses for development/fallback purposes
 */
function getMockCourses(): Course[] {
  return [
    {
      id: 1,
      title: "Introduction to AI and Machine Learning",
      description: "Learn the fundamentals of AI and ML algorithms",
      category: "Technology",
      thumbnail: "/uploads/course-thumbnails/ai-ml-intro.jpg",
      rating: 4.8,
      enrollmentCount: 1245,
      level: "Beginner",
      tags: ["AI", "Machine Learning", "Technology"],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    },
    {
      id: 2,
      title: "Advanced React Development",
      description: "Master React hooks, context API, and performance optimization",
      category: "Programming",
      thumbnail: "/uploads/course-thumbnails/react-advanced.jpg",
      rating: 4.7,
      enrollmentCount: 890,
      level: "Advanced",
      tags: ["React", "JavaScript", "Frontend"],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    },
    {
      id: 3,
      title: "Digital Marketing Masterclass",
      description: "Comprehensive guide to modern digital marketing strategies",
      category: "Marketing",
      thumbnail: "/uploads/course-thumbnails/digital-marketing.jpg",
      rating: 4.6,
      enrollmentCount: 1560,
      level: "Intermediate",
      tags: ["Marketing", "Social Media", "SEO"],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    },
    {
      id: 4,
      title: "Financial Planning for Entrepreneurs",
      description: "Essential financial skills for business founders",
      category: "Business",
      thumbnail: "/uploads/course-thumbnails/finance-entrepreneurs.jpg",
      rating: 4.9,
      enrollmentCount: 720,
      level: "Intermediate",
      tags: ["Finance", "Entrepreneurship", "Business"],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    },
    {
      id: 5,
      title: "Mobile App Development with Flutter",
      description: "Build cross-platform mobile apps with Flutter and Dart",
      category: "Programming",
      thumbnail: "/uploads/course-thumbnails/flutter-dev.jpg",
      rating: 4.5,
      enrollmentCount: 950,
      level: "Intermediate",
      tags: ["Flutter", "Mobile Development", "Dart"],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    },
    {
      id: 6,
      title: "Data Science with Python",
      description: "Comprehensive data analysis and visualization techniques",
      category: "Technology",
      thumbnail: "/uploads/course-thumbnails/data-science-python.jpg",
      rating: 4.7,
      enrollmentCount: 1120,
      level: "Intermediate",
      tags: ["Python", "Data Science", "Analytics"],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    },
    {
      id: 7,
      title: "Full-Stack Web Development",
      description: "End-to-end web application development with MERN stack",
      category: "Programming",
      thumbnail: "/uploads/course-thumbnails/fullstack-web.jpg",
      rating: 4.6,
      enrollmentCount: 840,
      level: "Advanced",
      tags: ["JavaScript", "React", "Node.js", "MongoDB"],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    },
    {
      id: 8,
      title: "UX/UI Design Principles",
      description: "Master the art of creating engaging user experiences",
      category: "Design",
      thumbnail: "/uploads/course-thumbnails/ux-ui-design.jpg",
      rating: 4.8,
      enrollmentCount: 760,
      level: "Beginner",
      tags: ["UX", "UI", "Design", "Figma"],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    },
    {
      id: 9,
      title: "Content Marketing Strategy",
      description: "Develop effective content strategies for digital platforms",
      category: "Marketing",
      thumbnail: "/uploads/course-thumbnails/content-marketing.jpg",
      rating: 4.5,
      enrollmentCount: 680,
      level: "Intermediate",
      tags: ["Content", "Marketing", "Strategy"],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    },
    {
      id: 10,
      title: "Cloud Computing with AWS",
      description: "Deploy and scale applications on Amazon Web Services",
      category: "Technology",
      thumbnail: "/uploads/course-thumbnails/aws-cloud.jpg",
      rating: 4.7,
      enrollmentCount: 910,
      level: "Advanced",
      tags: ["AWS", "Cloud", "DevOps"],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    },
    {
      id: 11,
      title: "Cybersecurity Fundamentals",
      description: "Essential security practices for the digital age",
      category: "Technology",
      thumbnail: "/uploads/course-thumbnails/cybersecurity.jpg",
      rating: 4.8,
      enrollmentCount: 830,
      level: "Beginner",
      tags: ["Security", "Networking", "Privacy"],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    },
    {
      id: 12,
      title: "Project Management Professional",
      description: "Prepare for PMP certification with comprehensive training",
      category: "Business",
      thumbnail: "/uploads/course-thumbnails/project-management.jpg",
      rating: 4.9,
      enrollmentCount: 1050,
      level: "Advanced",
      tags: ["Project Management", "PMP", "Leadership"],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    },
    {
      id: 13,
      title: "Graphic Design Masterclass",
      description: "From beginner to professional in modern graphic design",
      category: "Design",
      thumbnail: "/uploads/course-thumbnails/graphic-design.jpg",
      rating: 4.6,
      enrollmentCount: 940,
      level: "All Levels",
      tags: ["Graphic Design", "Illustrator", "Photoshop"],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    },
    {
      id: 14,
      title: "Blockchain Development",
      description: "Build decentralized applications and smart contracts",
      category: "Technology",
      thumbnail: "/uploads/course-thumbnails/blockchain-dev.jpg",
      rating: 4.7,
      enrollmentCount: 780,
      level: "Advanced",
      tags: ["Blockchain", "Ethereum", "Solidity"],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    },
    {
      id: 15,
      title: "Social Media Marketing",
      description: "Grow your brand with effective social media strategies",
      category: "Marketing",
      thumbnail: "/uploads/course-thumbnails/social-media.jpg",
      rating: 4.5,
      enrollmentCount: 1280,
      level: "Intermediate",
      tags: ["Social Media", "Marketing", "Branding"],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
  ];
}