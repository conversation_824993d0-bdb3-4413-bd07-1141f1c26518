import express, { Request, Response } from 'express';
import { storage } from '../storage';
import { z } from 'zod';
import Strip<PERSON> from 'stripe';

const router = express.Router();

// Initialize Stripe
if (!process.env.STRIPE_SECRET_KEY) {
  console.warn('STRIPE_SECRET_KEY not found. Payments will not be processed correctly.');
}

const stripe = process.env.STRIPE_SECRET_KEY 
  ? new Stripe(process.env.STRIPE_SECRET_KEY, { apiVersion: '2025-06-30.basil' })
  : null;

// Zod schemas for validation
const useCreditsSchema = z.object({
  amount: z.number().positive(),
  description: z.string().optional(),
  resourceId: z.number().optional(),
  resourceType: z.string().optional(),
});

const purchaseCreditsSchema = z.object({
  amount: z.number().positive(),
  paymentMethodId: z.string(),
});

// Constants for credit costs
const CREDITS_TO_DOLLAR_CONVERSION = 0.01; // 1 credit = $0.01

// Get user credits
router.get('/user/credits', async (req: Request, res: Response) => {
  if (!req.isAuthenticated()) {
    return res.status(401).json({ error: 'Not authenticated' });
  }

  try {
    const userId = req.user.id;
    const userStats = await storage.getUserStats(userId);
    
    if (!userStats) {
      return res.status(404).json({ error: 'User stats not found' });
    }
    
    // Get credit purchase history
    const purchaseHistory = await storage.getCreditPurchaseHistory(userId);
    
    // Calculate total purchases and last purchase date
    const totalPurchases = purchaseHistory.reduce((total, purchase) => {
      return purchase.amount > 0 ? total + purchase.amount : total;
    }, 0);
    
    // Sort by date and get the most recent purchase
    const sortedPurchases = [...purchaseHistory].sort((a, b) => 
      new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
    );
    
    const lastPurchase = sortedPurchases.find(p => p.amount > 0);
    const lastPurchaseDate = lastPurchase ? lastPurchase.createdAt : null;
    
    return res.status(200).json({
      balance: userStats.aiCredits || 0,
      lifetimeUsage: userStats.lifetimeCreditsUsed || 0,
      lastPurchaseDate,
      totalPurchases,
    });
  } catch (error) {
    console.error('Error fetching user credits:', error);
    return res.status(500).json({ 
      error: 'Failed to fetch credits',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Use credits
router.post('/user/credits/use', async (req: Request, res: Response) => {
  if (!req.isAuthenticated()) {
    return res.status(401).json({ error: 'Not authenticated' });
  }

  try {
    const result = useCreditsSchema.safeParse(req.body);
    if (!result.success) {
      return res.status(400).json({ 
        error: 'Invalid request data',
        details: result.error.format()
      });
    }

    const { amount, description, resourceId, resourceType } = result.data;
    const userId = req.user.id;
    
    // Check if user has enough credits
    const userStats = await storage.getUserStats(userId);
    if (!userStats || userStats.aiCredits < amount) {
      return res.status(400).json({
        error: 'Insufficient credits',
        creditsNeeded: amount,
        creditsAvailable: userStats ? userStats.aiCredits : 0
      });
    }

    // Deduct credits
    await storage.deductUserCredits(userId, amount);
    
    // Record credit transaction
    await storage.recordCreditTransaction({
      userId,
      amount: -amount,
      description: description || `Used ${amount} credits`,

      resourceId
    });
    
    // Get updated user stats
    const updatedStats = await storage.getUserStats(userId);
    
    return res.status(200).json({
      success: true,
      message: `Successfully used ${amount} credits`,
      credits: {
        balance: updatedStats?.aiCredits || 0,
        lifetimeUsage: updatedStats?.lifetimeCreditsUsed || 0,
        lastPurchaseDate: null, // This would be populated in GET /credits
        totalPurchases: 0 // This would be populated in GET /credits
      }
    });
  } catch (error) {
    console.error('Error using credits:', error);
    return res.status(500).json({ 
      error: 'Failed to use credits',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Purchase credits
router.post('/user/credits/purchase', async (req: Request, res: Response) => {
  if (!req.isAuthenticated()) {
    return res.status(401).json({ error: 'Not authenticated' });
  }

  try {
    const result = purchaseCreditsSchema.safeParse(req.body);
    if (!result.success) {
      return res.status(400).json({ 
        error: 'Invalid request data',
        details: result.error.format()
      });
    }

    const { amount, paymentMethodId } = result.data;
    const userId = req.user.id;
    
    // Calculate cost in USD
    const amountInCents = Math.round(amount * CREDITS_TO_DOLLAR_CONVERSION * 100);
    
    if (!stripe) {
      return res.status(500).json({ error: 'Payment processing is not configured' });
    }
    
    // Process payment with Stripe
    try {
      // Get or create customer
      let customerId = req.user.stripeCustomerId;
      
      if (!customerId) {
        const customer = await stripe.customers.create({
          email: req.user.email,
          name: req.user.name || req.user.username,
          payment_method: paymentMethodId,
          invoice_settings: {
            default_payment_method: paymentMethodId,
          },
        });
        
        customerId = customer.id;
        
        // Save customer ID to user record
        await storage.updateUserStripeId(userId, customerId);
      }
      
      // Create payment intent
      const paymentIntent = await stripe.paymentIntents.create({
        amount: amountInCents,
        currency: 'usd',
        customer: customerId,
        payment_method: paymentMethodId,
        confirm: true,
        description: `Purchase of ${amount} AI credits`,
      });
      
      if (paymentIntent.status === 'succeeded') {
        // Add credits to user account
        await storage.addUserCredits(userId, amount);
        
        // Record credit transaction
        await storage.recordCreditTransaction({
          userId,
          amount,
          description: `Purchased ${amount} credits`,

          metadata: {
            paymentIntentId: paymentIntent.id,
            amount: amountInCents / 100,
            currency: 'usd'
          }
        });
        
        // Get updated user stats
        const updatedStats = await storage.getUserStats(userId);
        
        return res.status(200).json({
          success: true,
          message: `Successfully purchased ${amount} credits`,
          credits: {
            balance: updatedStats?.aiCredits || 0,
            lifetimeUsage: updatedStats?.lifetimeCreditsUsed || 0,
            lastPurchaseDate: new Date(),
            totalPurchases: amount // This is just the current purchase, not the total
          }
        });
      } else {
        throw new Error(`Payment failed with status: ${paymentIntent.status}`);
      }
    } catch (stripeError) {
      console.error('Stripe error:', stripeError);
      return res.status(400).json({ 
        error: 'Payment processing failed',
        message: stripeError instanceof Error ? stripeError.message : 'Unknown payment error'
      });
    }
  } catch (error) {
    console.error('Error purchasing credits:', error);
    return res.status(500).json({ 
      error: 'Failed to purchase credits',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

export default router;