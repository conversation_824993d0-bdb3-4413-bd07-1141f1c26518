import OpenAI from "openai";
import { Quiz, QuizQuestion, QuizAnswer, QuizFlashcard } from "../shared-schema-alias";

// Initialize the OpenAI client
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

// The newest OpenAI model is "gpt-4o" which was released May 13, 2024
const DEFAULT_MODEL = "gpt-4o";

export interface QuizGenerationParams {
  courseId: number;
  moduleId?: number;
  lessonId?: number;
  courseTitle: string;
  courseDescription: string;
  lessonScript?: string;
  questionTypes: string[];
  numQuestions: number;
  difficulty: string;
  includeFlashcards: boolean;
  includeSummary: boolean;
}

export interface GeneratedQuiz {
  title: string;
  description: string;
  instructions?: string;
  questions: {
    questionType: string;
    questionText: string;
    explanation?: string;
    points: number;
    answers: {
      answerText: string;
      isCorrect: boolean;
      explanation?: string;
    }[];
  }[];
  flashcards?: {
    front: string;
    back: string;
    category?: string;
  }[];
  summary?: string;
  passingScore: number;
  timeLimit?: number;
  randomizeQuestions: boolean;
  showCorrectAnswers: boolean;
}

export async function generateQuiz(params: QuizGenerationParams): Promise<GeneratedQuiz> {
  try {
    const { 
      courseTitle, 
      courseDescription, 
      lessonScript, 
      questionTypes, 
      numQuestions, 
      difficulty,
      includeFlashcards,
      includeSummary
    } = params;
    
    // Only include allowed question types
    const validQuestionTypes = questionTypes.filter(type => 
      ["multiple-choice", "true-false", "short-answer"].includes(type)
    );
    
    if (validQuestionTypes.length === 0) {
      throw new Error("At least one valid question type must be selected");
    }
    
    // Build the prompt for OpenAI
    const systemPrompt = `
You are an expert educational assessment designer specializing in creating effective quizzes and tests 
for online courses. Your task is to create a comprehensive quiz based on the course content provided.
    `;
    
    const difficultyDescription = {
      easy: "Basic understanding and recall of main concepts. Questions should be straightforward.",
      medium: "Application of concepts and some analytical thinking. Moderate difficulty level appropriate for most students.",
      hard: "Complex analysis, synthesis of multiple concepts, and challenging questions that require deep understanding."
    };
    
    let prompt = `
Create a comprehensive quiz for a course with the following details:

Course Title: ${courseTitle}
Course Description: ${courseDescription}
${lessonScript ? `\nLesson Content:\n${lessonScript}\n` : ''}

Quiz Requirements:
- Difficulty Level: ${difficulty} (${difficultyDescription[difficulty as keyof typeof difficultyDescription]})
- Create exactly ${numQuestions} questions using ONLY these question types: ${validQuestionTypes.join(', ')}
- Include a balanced mix of the allowed question types
- Distribute questions to cover important concepts from the course content
- Each question should test understanding, not just memorization
- For each question, include an explanation of why the correct answer is right
- Multiple choice questions should have 4 options with only 1 correct answer
- True/False questions should be clearly verifiable from the course content
- Short answer questions should have clear, concise expected answers

${includeFlashcards ? `
Also create 8-10 flashcards that cover the key concepts. Each flashcard should have:
- Front side: A question or term
- Back side: The answer or definition
- A category label that groups similar flashcards
` : ''}

${includeSummary ? `
Please include a brief summary (2-3 paragraphs) of the key knowledge covered in this quiz.
The summary should reinforce the most important concepts that students should understand.
` : ''}

Format your response as a JSON object with the following structure:
{
  "title": "Quiz title",
  "description": "Brief description of what the quiz covers",
  "instructions": "Instructions for quiz takers",
  "questions": [
    {
      "questionType": "multiple-choice",
      "questionText": "The question text goes here",
      "explanation": "Explanation of why the correct answer is right",
      "points": 1,
      "answers": [
        {
          "answerText": "Option A",
          "isCorrect": true,
          "explanation": "Optional explanation for this specific option"
        },
        {
          "answerText": "Option B",
          "isCorrect": false
        },
        ...
      ]
    },
    {
      "questionType": "true-false",
      "questionText": "A true/false question goes here",
      "explanation": "Explanation of why the answer is true or false",
      "points": 1,
      "answers": [
        {
          "answerText": "True",
          "isCorrect": true
        },
        {
          "answerText": "False",
          "isCorrect": false
        }
      ]
    },
    {
      "questionType": "short-answer",
      "questionText": "A short answer question goes here",
      "explanation": "Explanation of the expected answer",
      "points": 1,
      "answers": [
        {
          "answerText": "The expected answer",
          "isCorrect": true
        }
      ]
    }
  ],
  ${includeFlashcards ? `
  "flashcards": [
    {
      "front": "Term or question",
      "back": "Definition or answer",
      "category": "Category for grouping similar flashcards"
    },
    ...
  ],
  ` : ''}
  ${includeSummary ? `"summary": "Knowledge summary text here",` : ''}
  "passingScore": 70,
  "randomizeQuestions": false,
  "showCorrectAnswers": true
}
`;

    // Make the API call
    const response = await openai.chat.completions.create({
      model: DEFAULT_MODEL,
      messages: [
        { role: "system", content: systemPrompt },
        { role: "user", content: prompt }
      ],
      response_format: { type: "json_object" },
      temperature: 0.5,
    });

    // Parse the response
    const jsonResponse = JSON.parse(response.choices[0].message.content || "{}");
    
    // Basic validation of the response
    if (!jsonResponse.questions || !Array.isArray(jsonResponse.questions) || jsonResponse.questions.length === 0) {
      throw new Error("Failed to generate quiz questions");
    }
    
    // Ensure the number of questions matches the request
    let quizQuestions = jsonResponse.questions;
    if (quizQuestions.length > numQuestions) {
      quizQuestions = quizQuestions.slice(0, numQuestions);
    }
    
    // Default values for any missing fields
    const generatedQuiz: GeneratedQuiz = {
      title: jsonResponse.title || `${courseTitle} - Assessment Quiz`,
      description: jsonResponse.description || `A quiz covering the key concepts from ${courseTitle}`,
      instructions: jsonResponse.instructions,
      questions: quizQuestions,
      flashcards: jsonResponse.flashcards || [],
      summary: jsonResponse.summary,
      passingScore: jsonResponse.passingScore || 70,
      timeLimit: jsonResponse.timeLimit,
      randomizeQuestions: jsonResponse.randomizeQuestions || false,
      showCorrectAnswers: jsonResponse.showCorrectAnswers !== undefined ? jsonResponse.showCorrectAnswers : true
    };
    
    return generatedQuiz;
  } catch (error) {
    console.error("Error generating quiz with OpenAI:", error);
    throw new Error("Failed to generate quiz: " + (error instanceof Error ? error.message : "Unknown error"));
  }
}