/**
 * This file was auto-generated by <PERSON><PERSON> from our API Definition.
 */
import * as Eleven<PERSON>ab<PERSON> from "../index";
/**
 * The type of tool
 */
export type PromptAgentOutputToolsItem = ElevenLabs.PromptAgentOutputToolsItem.Client | ElevenLabs.PromptAgentOutputToolsItem.Mcp | ElevenLabs.PromptAgentOutputToolsItem.System | ElevenLabs.PromptAgentOutputToolsItem.Webhook;
export declare namespace PromptAgentOutputToolsItem {
    interface Client extends ElevenLabs.ClientToolConfigOutput {
        type: "client";
    }
    interface Mcp extends ElevenLabs.McpToolConfigOutput {
        type: "mcp";
    }
    interface System extends ElevenLabs.SystemToolConfigOutput {
        type: "system";
    }
    interface Webhook extends ElevenLabs.WebhookToolConfigOutput {
        type: "webhook";
    }
}
