{"type": "module", "name": "form-data-encoder", "description": "Encode FormData content into the multipart/form-data format", "version": "4.1.0", "author": "<PERSON>", "license": "MIT", "repository": "octet-stream/form-data-encoder", "sideEffects": false, "engines": {"node": ">= 18"}, "keywords": ["form-data", "encoder", "multipart", "files-upload", "async-iterator", "spec-compatible", "form"], "files": ["lib"], "main": "./lib/index.js", "module": "./lib/index.js", "exports": {"./package.json": "./package.json", ".": {"import": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "require": {"types": "./lib/index.d.cts", "default": "./lib/index.cjs"}}}, "types": "./lib/index.d.ts", "devDependencies": {"@biomejs/biome": "1.8.3", "@changesets/changelog-github": "0.5.0", "@changesets/cli": "2.27.6", "@types/mime-types": "2.1.4", "@types/node": "20.14.2", "@types/sinon": "17.0.3", "ava": "6.1.3", "c8": "10.1.2", "cross-env": "7.0.3", "del-cli": "5.1.0", "formdata-node": "6.0.3", "husky": "9.0.11", "lint-staged": "15.2.7", "node-fetch": "3.3.2", "sinon": "18.0.0", "ts-expect": "1.3.0", "ts-node": "10.9.2", "tsup": "8.1.0", "typescript": "5.5.2", "undici": "6.19.2", "web-streams-polyfill": "4.0.0"}, "scripts": {"lint:types": "tsc --project tsconfig.json --noEmit", "lint": "biome lint --write --no-errors-on-unmatched . && pnpm lint:types", "coverage": "c8 pnpm test", "ci": "c8 pnpm test && c8 report --reporter=json", "build": "pnpm exec del-cli lib && pnpm exec tsup", "test": "cross-env NODE_OPTIONS=\"--no-warnings --experimental-fetch --loader=ts-node/esm\" ava", "report:html": "c8 -r=html pnpm test", "release": "pnpm build && pnpm changeset publish"}}