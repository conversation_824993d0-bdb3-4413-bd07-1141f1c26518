import { spawn } from 'child_process';
import path from 'path';
import fs from 'fs/promises';

interface Scene {
  id: string;
  text: string;
  duration: number;
  media: any;
  visualDescription: string;
}

interface VideoSettings {
  resolution: string;
  fps: number;
  duration: number;
}

interface EnhancedVideoParams {
  scenes: Scene[];
  audio: any;
  style: string;
  title: string;
  settings: VideoSettings;
}

// File utilities
async function ensureDirectoryExists(dirPath: string): Promise<void> {
  try {
    await fs.access(dirPath);
  } catch {
    await fs.mkdir(dirPath, { recursive: true });
  }
}

async function downloadFile(url: string, localPath: string): Promise<void> {
  if (url.startsWith('/api/placeholder/')) {
    // Create a placeholder file
    await fs.writeFile(localPath, Buffer.alloc(1024));
    return;
  }
  
  const response = await fetch(url);
  if (!response.ok) {
    throw new Error(`Failed to download: ${response.status}`);
  }
  
  const buffer = await response.arrayBuffer();
  await fs.writeFile(localPath, Buffer.from(buffer));
}

export async function createEnhancedVideo(params: EnhancedVideoParams): Promise<any> {
  const { scenes, audio, style, title, settings } = params;
  
  try {
    console.log('Creating enhanced video:', title, 'with', scenes.length, 'scenes');
    
    // Create working directories
    const workDir = path.join(process.cwd(), 'temp', `video_${Date.now()}`);
    const outputDir = path.join(process.cwd(), 'uploads', 'videos');
    
    await ensureDirectoryExists(workDir);
    await ensureDirectoryExists(outputDir);
    
    // Download and prepare media files
    const mediaFiles = [];
    for (let i = 0; i < scenes.length; i++) {
      const scene = scenes[i];
      let mediaPath: string;
      
      if (scene.media?.url) {
        const ext = scene.media.type === 'video' ? '.mp4' : '.jpg';
        mediaPath = path.join(workDir, `scene_${i}${ext}`);
        try {
          await downloadFile(scene.media.url, mediaPath);
        } catch (downloadError) {
          console.warn(`Failed to download media for scene ${i}, using placeholder`);
          mediaPath = await createPlaceholderImage(workDir, i, scene.text);
        }
      } else {
        // Create placeholder image with scene text
        mediaPath = await createPlaceholderImage(workDir, i, scene.text);
      }
      
      mediaFiles.push({
        path: mediaPath,
        duration: scene.duration || 5,
        text: scene.text
      });
    }
    
    // Handle audio - prioritize actual audio file over base64
    let audioPath: string;
    if (audio?.audioUrl && !audio.audioUrl.includes('placeholder')) {
      audioPath = path.join(workDir, 'audio.mp3');
      try {
        await downloadFile(audio.audioUrl, audioPath);
      } catch (audioError) {
        console.warn('Failed to download audio, generating silent audio');
        audioPath = await createSilentAudio(workDir, settings.duration);
      }
    } else if (typeof audio === 'string' && audio.startsWith('data:')) {
      // Handle base64 audio
      const audioBuffer = Buffer.from(audio.split(',')[1], 'base64');
      audioPath = path.join(workDir, 'audio.mp3');
      await fs.writeFile(audioPath, audioBuffer);
    } else {
      // Generate silent audio as fallback
      audioPath = await createSilentAudio(workDir, settings.duration);
    }
    
    // Create video with FFmpeg
    const outputFileName = `lesson_${Date.now()}.mp4`;
    const outputPath = path.join(outputDir, outputFileName);
    
    await createVideoWithFFmpeg({
      mediaFiles,
      audioPath,
      outputPath,
      settings,
      title
    });
    
    // Clean up working directory
    try {
      await fs.rm(workDir, { recursive: true, force: true });
    } catch (cleanupError) {
      console.warn('Failed to cleanup working directory:', cleanupError);
    }
    
    return {
      success: true,
      videoUrl: `/api/video/generated/${outputFileName}`,
      thumbnailUrl: `/api/placeholder/800x450`,
      duration: settings.duration,
      sceneCount: scenes.length,
      metadata: {
        title,
        style,
        resolution: settings.resolution,
        fps: settings.fps
      }
    };
    
  } catch (error) {
    console.error('Enhanced video creation error:', error);
    throw error;
  }
}

async function createPlaceholderImage(workDir: string, index: number, text: string): Promise<string> {
  const imagePath = path.join(workDir, `placeholder_${index}.jpg`);
  
  // Create a simple colored background image using ImageMagick
  try {
    await new Promise((resolve, reject) => {
      const convert = spawn('convert', [
        '-size', '1920x1080',
        '-background', '#2563eb',
        '-fill', 'white',
        '-gravity', 'center',
        '-pointsize', '72',
        '-font', 'Arial',
        `label:Scene ${index + 1}`,
        imagePath
      ]);
      
      convert.on('close', (code) => {
        if (code === 0) resolve(undefined);
        else reject(new Error(`ImageMagick failed with code ${code}`));
      });
      
      convert.on('error', reject);
    });
  } catch (imageError) {
    console.warn('ImageMagick not available, creating basic file');
    // Create a basic placeholder file
    await fs.writeFile(imagePath, Buffer.alloc(1024));
  }
  
  return imagePath;
}

async function createSilentAudio(workDir: string, duration: number): Promise<string> {
  const audioPath = path.join(workDir, 'silent.mp3');
  
  return new Promise((resolve, reject) => {
    const ffmpeg = spawn('ffmpeg', [
      '-f', 'lavfi',
      '-i', `anullsrc=channel_layout=stereo:sample_rate=44100`,
      '-t', duration.toString(),
      '-c:a', 'mp3',
      '-y',
      audioPath
    ]);
    
    ffmpeg.on('close', (code) => {
      if (code === 0) resolve(audioPath);
      else reject(new Error(`Silent audio generation failed with code ${code}`));
    });
    
    ffmpeg.on('error', reject);
  });
}

async function createVideoWithFFmpeg(params: {
  mediaFiles: Array<{path: string, duration: number, text: string}>;
  audioPath: string;
  outputPath: string;
  settings: VideoSettings;
  title: string;
}): Promise<void> {
  const { mediaFiles, audioPath, outputPath, settings, title } = params;
  
  return new Promise((resolve, reject) => {
    // Simple FFmpeg command for slideshow with audio
    const ffmpegArgs = [
      '-y', // Overwrite output file
    ];
    
    // Add all image inputs
    mediaFiles.forEach(media => {
      ffmpegArgs.push('-loop', '1', '-i', media.path);
    });
    
    // Add audio input
    ffmpegArgs.push('-i', audioPath);
    
    // Create filter for slideshow
    let filterComplex = '';
    for (let i = 0; i < mediaFiles.length; i++) {
      if (i > 0) filterComplex += '; ';
      filterComplex += `[${i}:v]scale=1920:1080:force_original_aspect_ratio=decrease,pad=1920:1080:-1:-1:black,setpts=PTS-STARTPTS[v${i}]`;
    }
    
    // Concatenate videos
    filterComplex += '; ';
    for (let i = 0; i < mediaFiles.length; i++) {
      filterComplex += `[v${i}]`;
    }
    filterComplex += `concat=n=${mediaFiles.length}:v=1:a=0[vout]`;
    
    ffmpegArgs.push(
      '-filter_complex', filterComplex,
      '-map', '[vout]',
      '-map', `${mediaFiles.length}:a`, // Audio from last input
      '-c:v', 'libx264',
      '-c:a', 'aac',
      '-preset', 'fast',
      '-crf', '23',
      '-r', '30',
      '-t', '30', // Fixed 30 second duration
      '-movflags', '+faststart',
      outputPath
    );
    
    console.log('Creating video with FFmpeg:', title);
    
    const ffmpeg = spawn('ffmpeg', ffmpegArgs);
    
    let stderrData = '';
    ffmpeg.stderr.on('data', (data) => {
      stderrData += data.toString();
    });
    
    ffmpeg.on('close', (code) => {
      if (code === 0) {
        console.log('Video created successfully:', outputPath);
        resolve();
      } else {
        console.error('FFmpeg stderr:', stderrData);
        reject(new Error(`FFmpeg process exited with code ${code}`));
      }
    });
    
    ffmpeg.on('error', (error) => {
      console.error('FFmpeg spawn error:', error);
      reject(error);
    });
  });
}

// Export alias for backward compatibility
export const enhancedVideoPipeline = createEnhancedVideo;