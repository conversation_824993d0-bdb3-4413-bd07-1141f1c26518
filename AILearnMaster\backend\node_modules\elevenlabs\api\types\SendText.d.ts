/**
 * This file was auto-generated by <PERSON><PERSON> from our API Definition.
 */
import * as Eleven<PERSON>abs from "../index";
export interface SendText {
    /** The text to be sent to the API for audio generation. Should always end with a single space string. */
    text: string;
    /**
     * This is an advanced setting that most users shouldn't need to use. It relates to our generation schedule.
     *
     * Use this to attempt to immediately trigger the generation of audio, overriding the `chunk_length_schedule`.
     * Unlike flush, `try_trigger_generation` will only generate audio if our
     * buffer contains more than a minimum
     * threshold of characters, this is to ensure a higher quality response from our model.
     *
     * Note that overriding the chunk schedule to generate small amounts of
     * text may result in lower quality audio, therefore, only use this parameter if you
     * really need text to be processed immediately. We generally recommend keeping the default value of
     * `false` and adjusting the `chunk_length_schedule` in the `generation_config` instead.
     */
    try_trigger_generation?: boolean;
    /** The voice settings field can be provided in the first `InitializeConnection` message and then must either be not provided or not changed. */
    voice_settings?: ElevenLabs.RealtimeVoiceSettings;
    /** The generator config field can be provided in the first `InitializeConnection` message and then must either be not provided or not changed. */
    generator_config?: ElevenLabs.GenerationConfig;
    /**
     * Flush forces the generation of audio. Set this value to true when you have finished sending text, but want to keep the websocket connection open.
     *
     * This is useful when you want to ensure that the last chunk of audio is generated even when the length of text sent is smaller than the value set in chunk_length_schedule (e.g. 120 or 50).
     */
    flush?: boolean;
}
