/**
 * Database Health Monitoring Routes
 * Comprehensive database status, performance metrics, and diagnostics
 */

import express, { Request, Response } from 'express';
import { dbManager, getDatabaseStatus, safeDbOperation } from '../db-enhanced';
import { sql } from 'drizzle-orm';
import { users, courses, modules, lessons } from '@shared/schema';

const router = express.Router();

/**
 * Basic database health check
 */
router.get('/health', async (req: Request, res: Response) => {
  try {
    const status = getDatabaseStatus();
    
    // Test basic connectivity
    const isHealthy = await safeDbOperation(async () => {
      const db = dbManager.getDatabase();
      const result = await db.execute(sql`SELECT 1 as test, NOW() as timestamp`);
      return result.rows && result.rows.length > 0;
    });

    const healthStatus = {
      status: isHealthy ? 'healthy' : 'unhealthy',
      timestamp: new Date().toISOString(),
      database: {
        connected: status.isConnected,
        lastHealthCheck: status.lastHealthCheck,
        connectionAttempts: status.connectionAttempts,
        lastError: status.lastError,
        environment: status.config.environment,
        maxConnections: status.config.maxConnections
      },
      connectivity: {
        canConnect: isHealthy,
        responseTime: null as number | null
      }
    };

    // Measure response time
    if (isHealthy) {
      const startTime = Date.now();
      await safeDbOperation(async () => {
        const db = dbManager.getDatabase();
        await db.execute(sql`SELECT 1`);
      });
      healthStatus.connectivity.responseTime = Date.now() - startTime;
    }

    const httpStatus = isHealthy ? 200 : 503;
    res.status(httpStatus).json(healthStatus);
    
  } catch (error) {
    console.error('Database health check failed:', error);
    res.status(503).json({
      status: 'error',
      timestamp: new Date().toISOString(),
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * Detailed database diagnostics
 */
router.get('/diagnostics', async (req: Request, res: Response) => {
  try {
    const status = getDatabaseStatus();
    
    // Get database statistics
    const stats = await safeDbOperation(async () => {
      const db = dbManager.getDatabase();
      
      // Get table counts
      const [userCount] = await db.execute(sql`SELECT COUNT(*) as count FROM users`);
      const [courseCount] = await db.execute(sql`SELECT COUNT(*) as count FROM courses`);
      const [moduleCount] = await db.execute(sql`SELECT COUNT(*) as count FROM modules`);
      const [lessonCount] = await db.execute(sql`SELECT COUNT(*) as count FROM lessons`);
      
      // Get database size information
      const [dbSize] = await db.execute(sql`
        SELECT 
          pg_size_pretty(pg_database_size(current_database())) as database_size,
          current_database() as database_name
      `);
      
      // Get connection information using safe safe parameterized query
      const [connectionInfo] = await db.execute(sql`
        SELECT
          count(*) as active_connections,
          max_conn.setting as max_connections
        FROM pg_stat_activity,
             (SELECT setting FROM pg_settings WHERE name = 'max_connections') max_conn
        WHERE state = 'active'
        GROUP BY max_conn.setting
      `);
      
      return {
        tables: {
          users: parseInt(userCount.count as string),
          courses: parseInt(courseCount.count as string),
          modules: parseInt(moduleCount.count as string),
          lessons: parseInt(lessonCount.count as string)
        },
        database: {
          name: dbSize.database_name,
          size: dbSize.database_size
        },
        connections: {
          active: parseInt(connectionInfo?.active_connections as string || '0'),
          max: parseInt(connectionInfo?.max_connections as string || '0')
        }
      };
    });

    const diagnostics = {
      timestamp: new Date().toISOString(),
      status: status.isConnected ? 'connected' : 'disconnected',
      connection: {
        isConnected: status.isConnected,
        lastHealthCheck: status.lastHealthCheck,
        connectionAttempts: status.connectionAttempts,
        lastError: status.lastError,
        maxConnections: status.config.maxConnections,
        environment: status.config.environment
      },
      statistics: stats || {
        tables: { users: 0, courses: 0, modules: 0, lessons: 0 },
        database: { name: 'unknown', size: 'unknown' },
        connections: { active: 0, max: 0 }
      },
      performance: {
        queryResponseTime: null as number | null,
        lastMeasured: new Date().toISOString()
      }
    };

    // Measure query performance
    if (status.isConnected) {
      const startTime = Date.now();
      await safeDbOperation(async () => {
        const db = dbManager.getDatabase();
        await db.execute(sql`SELECT COUNT(*) FROM users LIMIT 1`);
      });
      diagnostics.performance.queryResponseTime = Date.now() - startTime;
    }

    res.json(diagnostics);
    
  } catch (error) {
    console.error('Database diagnostics failed:', error);
    res.status(500).json({
      error: 'Failed to retrieve database diagnostics',
      details: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * Database performance metrics
 */
router.get('/metrics', async (req: Request, res: Response) => {
  try {
    const metrics = await safeDbOperation(async () => {
      const db = dbManager.getDatabase();
      
      // Get table sizes
      const tableSizes = await db.execute(sql`
        SELECT 
          schemaname,
          tablename,
          pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size,
          pg_total_relation_size(schemaname||'.'||tablename) as size_bytes
        FROM pg_tables 
        WHERE schemaname = 'public'
        ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC
        LIMIT 10
      `);
      
      // Get index usage
      const indexUsage = await db.execute(sql`
        SELECT 
          schemaname,
          tablename,
          indexname,
          idx_scan as scans,
          idx_tup_read as tuples_read,
          idx_tup_fetch as tuples_fetched
        FROM pg_stat_user_indexes 
        ORDER BY idx_scan DESC
        LIMIT 10
      `);
      
      // Get slow queries (if available)
      const slowQueries = await db.execute(sql`
        SELECT 
          query,
          calls,
          total_time,
          mean_time,
          rows
        FROM pg_stat_statements 
        ORDER BY mean_time DESC
        LIMIT 5
      `).catch(() => []);
      
      return {
        tableSizes,
        indexUsage,
        slowQueries
      };
    });

    const performanceMetrics = {
      timestamp: new Date().toISOString(),
      database: {
        tableSizes: metrics?.tableSizes || [],
        indexUsage: metrics?.indexUsage || [],
        slowQueries: metrics?.slowQueries || []
      },
      application: {
        environment: process.env.NODE_ENV || 'development',
        nodeVersion: process.version,
        uptime: process.uptime()
      }
    };

    res.json(performanceMetrics);
    
  } catch (error) {
    console.error('Database metrics failed:', error);
    res.status(500).json({
      error: 'Failed to retrieve database metrics',
      details: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * Test database operations
 */
router.post('/test', async (req: Request, res: Response) => {
  try {
    const testResults = {
      timestamp: new Date().toISOString(),
      tests: {
        connection: false,
        read: false,
        write: false,
        transaction: false
      },
      timings: {
        connection: 0,
        read: 0,
        write: 0,
        transaction: 0
      },
      errors: [] as string[]
    };

    // Test 1: Connection
    let startTime = Date.now();
    try {
      await safeDbOperation(async () => {
        const db = dbManager.getDatabase();
        await db.execute(sql`SELECT 1`);
      });
      testResults.tests.connection = true;
      testResults.timings.connection = Date.now() - startTime;
    } catch (error) {
      testResults.errors.push(`Connection test failed: ${error}`);
    }

    // Test 2: Read operation
    startTime = Date.now();
    try {
      await safeDbOperation(async () => {
        const db = dbManager.getDatabase();
        await db.execute(sql`SELECT COUNT(*) FROM users LIMIT 1`);
      });
      testResults.tests.read = true;
      testResults.timings.read = Date.now() - startTime;
    } catch (error) {
      testResults.errors.push(`Read test failed: ${error}`);
    }

    // Test 3: Write operation (create and delete test record)
    startTime = Date.now();
    try {
      await safeDbOperation(async () => {
        const db = dbManager.getDatabase();
        // Note: This is a read-only test to avoid creating actual test data
        await db.execute(sql`SELECT 'write_test' as test`);
      });
      testResults.tests.write = true;
      testResults.timings.write = Date.now() - startTime;
    } catch (error) {
      testResults.errors.push(`Write test failed: ${error}`);
    }

    // Test 4: Transaction
    startTime = Date.now();
    try {
      await safeDbOperation(async () => {
        const db = dbManager.getDatabase();
        await db.transaction(async (tx) => {
          await tx.execute(sql`SELECT 'transaction_test' as test`);
        });
      });
      testResults.tests.transaction = true;
      testResults.timings.transaction = Date.now() - startTime;
    } catch (error) {
      testResults.errors.push(`Transaction test failed: ${error}`);
    }

    const allTestsPassed = Object.values(testResults.tests).every(test => test);
    const httpStatus = allTestsPassed ? 200 : 500;

    res.status(httpStatus).json({
      ...testResults,
      summary: {
        allTestsPassed,
        passedTests: Object.values(testResults.tests).filter(test => test).length,
        totalTests: Object.keys(testResults.tests).length,
        totalTime: Object.values(testResults.timings).reduce((sum, time) => sum + time, 0)
      }
    });
    
  } catch (error) {
    console.error('Database test failed:', error);
    res.status(500).json({
      error: 'Database test suite failed',
      details: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    });
  }
});

export default router;
