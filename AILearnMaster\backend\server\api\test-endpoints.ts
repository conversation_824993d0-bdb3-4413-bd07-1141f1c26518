import { Router, Request, Response } from "express";
import OpenAI from "openai";
import * as openAIFallbackService from "../services/openAIFallbackService";
import { z } from "zod";

export const testApiRouter = Router();

// Test OpenAI API endpoint
testApiRouter.get("/test-openai", async (req: Request, res: Response) => {
  try {
    if (!req.session?.userId) {
      return res.status(401).json({ message: "Not authenticated" });
    }
    
    // Simple test to make sure OpenAI is configured correctly
    const openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });
    
    const response = await openai.chat.completions.create({
      model: "gpt-4o", // the newest OpenAI model is "gpt-4o" which was released May 13, 2024. do not change this unless explicitly requested by the user
      messages: [{ role: "user", content: "Hello, OpenAI! Generate a one-sentence response about online learning." }],
      max_tokens: 100
    });
    
    // Also test image generation
    const imageResponse = await openai.images.generate({
      model: "dall-e-3",
      prompt: "A digital classroom of the future with AI assistants helping students learn",
      n: 1,
      size: "1024x1024",
      quality: "standard"
    });
    
    return res.status(200).json({
      success: true,
      message: "OpenAI API is working!",
      textResponse: response.choices[0].message,
      imageUrl: imageResponse.data[0]?.url || null
    });
  } catch (error: any) {
    console.error("OpenAI API error:", error);
    return res.status(500).json({ 
      success: false, 
      message: "OpenAI API call failed", 
      error: error.message 
    });
  }
});

// Add the test endpoints for video generation
testApiRouter.post("/video-lessons/generate", async (req: Request, res: Response) => {
  try {
    if (!req.session?.userId) {
      return res.status(401).json({ message: "Not authenticated" });
    }
    
    const { lessonTitle, script, voiceId, subtitles } = req.body;
    
    if (!lessonTitle || !script) {
      return res.status(400).json({ message: "Lesson title and script are required" });
    }
    
    // Import the videoLessonGenerator service
    const videoService = await import('../services/videoLessonGenerator');
    
    // Start video generation job
    const jobId = await videoService.startVideoLessonGeneration({
      userId: req.session.userId,
      lessonTitle,
      script,
      voiceId: voiceId || undefined,
      subtitles: !!subtitles
    });
    
    const status = videoService.getVideoLessonStatus(jobId.id);
    
    return res.status(200).json({
      message: "Video generation job started",
      id: jobId,
      status: status?.status || 'queued',
      progress: status?.progress || 0,
      estimatedCompletionTime: status?.estimatedCompletionTime || 0
    });
  } catch (error: any) {
    console.error("Video generation error:", error);
    return res.status(500).json({
      message: "Failed to start video generation",
      error: error.message
    });
  }
});

testApiRouter.get("/video-lessons/status/:id", async (req: Request, res: Response) => {
  try {
    if (!req.session?.userId) {
      return res.status(401).json({ message: "Not authenticated" });
    }
    
    const jobId = req.params.id;
    
    if (!jobId) {
      return res.status(400).json({ message: "Job ID is required" });
    }
    
    // Import the videoLessonGenerator service
    const videoService = await import('../services/videoLessonGenerator');
    
    // Get job status
    const status = videoService.getVideoLessonStatus(jobId);
    
    if (!status) {
      return res.status(404).json({ message: "Job not found" });
    }
    
    return res.status(200).json(status);
  } catch (error: any) {
    console.error("Get video status error:", error);
    return res.status(500).json({
      message: "Failed to get video status",
      error: error.message
    });
  }
});