/**
 * This file was auto-generated by <PERSON>rn from our API Definition.
 */
import * as ElevenLabs from "../../../../index";
/**
 * @example
 *     {
 *         featured: true,
 *         reader_app_enabled: true
 *     }
 */
export interface VoicesGetSharedRequest {
    /**
     * How many shared voices to return at maximum. Can not exceed 100, defaults to 30.
     */
    page_size?: number;
    /**
     * Voice category used for filtering
     */
    category?: ElevenLabs.VoicesGetSharedRequestCategory;
    /**
     * Gender used for filtering
     */
    gender?: string;
    /**
     * Age used for filtering
     */
    age?: string;
    /**
     * Accent used for filtering
     */
    accent?: string;
    /**
     * Language used for filtering
     */
    language?: string;
    /**
     * Locale used for filtering
     */
    locale?: string;
    /**
     * Search term used for filtering
     */
    search?: string;
    /**
     * Use-case used for filtering
     */
    use_cases?: string | string[];
    /**
     * Search term used for filtering
     */
    descriptives?: string | string[];
    /**
     * Filter featured voices
     */
    featured?: boolean;
    /**
     * Filter voices with a minimum notice period of the given number of days.
     */
    min_notice_period_days?: number;
    /**
     * Include/exclude voices with custom rates
     */
    include_custom_rates?: boolean;
    /**
     * Filter voices that are enabled for the reader app
     */
    reader_app_enabled?: boolean;
    /**
     * Filter voices by public owner ID
     */
    owner_id?: string;
    /**
     * Sort criteria
     */
    sort?: string;
    page?: number;
}
