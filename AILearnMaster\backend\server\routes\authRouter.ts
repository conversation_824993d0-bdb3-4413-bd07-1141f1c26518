import express, { Request, Response } from 'express';
import { storage } from '../storage';
import { AuthService } from '../services/auth';
import { insertUserSchema, users } from '@shared/schema';
import { db } from '../db';
import { eq } from 'drizzle-orm';
import rateLimit from 'express-rate-limit';

const router = express.Router();

// Rate limiting for authentication endpoints
const authLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // Limit each IP to 5 requests per windowMs
  message: {
    error: 'Too many authentication attempts, please try again later.',
    retryAfter: '15 minutes'
  },
  standardHeaders: true,
  legacyHeaders: false,
});

const registerLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 3, // Limit each IP to 3 registration attempts per hour
  message: {
    error: 'Too many registration attempts, please try again later.',
    retryAfter: '1 hour'
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Login route with rate limiting
router.post("/login", authLimiter, async (req: Request, res: Response) => {
  try {
    console.log("Login request received:", { body: req.body });
    
    const { username, password } = req.body;
    
    if (!username || !password) {
      console.log("Missing username or password");
      return res.status(400).json({ message: "Username and password are required" });
    }
    
    console.log("Looking up user:", username);
    const user = await storage.getUserByUsername(username);
    
    if (!user) {
      console.log("User not found:", username);
      return res.status(401).json({ message: "Invalid credentials" });
    }
    
    console.log("User found, verifying password");
    // Verify password
    const isValidPassword = await AuthService.comparePassword(password, user.password);
    
    if (!isValidPassword) {
      console.log("Invalid password for user:", username);
      return res.status(401).json({ message: "Invalid credentials" });
    }
    
    console.log("Password valid, creating session");
    // Set user in session
    if (req.session) {
      req.session.userId = user.id;
    }
    
    // Create safe user object (excluding password)
    const safeUser = AuthService.createSafeUser(user);
    
    console.log("Login successful for user:", username);
    return res.status(200).json(safeUser);
  } catch (error) {
    console.error("Login error:", error);
    return res.status(500).json({ message: "Server error", error: error.message });
  }
});

// Register route with rate limiting
router.post("/register", registerLimiter, async (req: Request, res: Response) => {
  try {
    const result = insertUserSchema.safeParse(req.body);
    
    if (!result.success) {
      return res.status(400).json({ message: "Invalid user data", errors: result.error.format() });
    }
    
    const { username, email, password, name } = result.data;
    
    // Check if username already exists
    const existingUser = await storage.getUserByUsername(username);
    if (existingUser) {
      return res.status(400).json({ message: "Username already taken" });
    }
    
    // Check if email already exists
    const existingEmail = await storage.getUserByEmail(email);
    if (existingEmail) {
      return res.status(400).json({ message: "Email already in use" });
    }
    
    // Hash password
    const hashedPassword = await AuthService.hashPassword(password);
    
    // Create user
    const newUser = await storage.createUser({
      username,
      email,
      password: hashedPassword,
      name,
      role: 'user',
      plan: 'free',
      isVerified: false,
      verificationToken: AuthService.generateRandomToken(),
      createdAt: new Date(),
      updatedAt: new Date()
    });
    
    // Create safe user object (excluding password)
    const safeUser = AuthService.createSafeUser(newUser);
    
    // Set user in session
    if (req.session) {
      req.session.userId = newUser.id;
    }
    
    return res.status(201).json(safeUser);
  } catch (error) {
    console.error("Registration error:", error);
    return res.status(500).json({ message: "Server error" });
  }
});

// Get current user
router.get("/me", async (req: Request, res: Response) => {
  try {
    if (!req.session || !req.session.userId) {
      return res.status(401).json({ message: "Not authenticated" });
    }
    
    // Get user directly from database by ID
    const userId = req.session.userId as number;
    const user = await storage.getUser(userId);
    
    if (!user) {
      req.session.destroy(() => {});
      return res.status(401).json({ message: "User not found" });
    }
    
    // Create safe user object (excluding password)
    const safeUser = AuthService.createSafeUser(user);
    
    return res.status(200).json(safeUser);
  } catch (error) {
    console.error("Auth/me error:", error);
    return res.status(500).json({ message: "Server error" });
  }
});

// Logout
router.post("/logout", (req: Request, res: Response) => {
  if (req.session) {
    req.session.destroy(() => {
      res.status(200).json({ success: true });
    });
  } else {
    res.status(200).json({ success: true });
  }
});

export default router;