{"name": "ailearn-backend", "version": "1.0.0", "type": "module", "license": "MIT", "description": "AI Learn Master Backend API", "main": "server/index.js", "scripts": {"dev": "tsx server/index.ts", "build": "esbuild server/index.ts --platform=node --packages=external --bundle --format=esm --outdir=dist", "start": "NODE_ENV=production node dist/index.js", "check": "tsc --noEmit", "db:push": "drizzle-kit push", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:studio": "drizzle-kit studio", "db:setup": "tsx scripts/database-setup.ts", "db:setup:force": "tsx scripts/database-setup.ts --force", "db:validate": "tsx scripts/database-setup.ts --validate-only", "db:report": "tsx scripts/database-setup.ts --report", "db:health": "curl -s http://localhost:3001/api/database/health | jq", "db:diagnostics": "curl -s http://localhost:3001/api/database/diagnostics | jq", "db:test": "curl -s -X POST http://localhost:3001/api/database/test | jq", "db:test-functionality": "tsx scripts/test-database-functionality.ts", "test:course-workflows": "tsx scripts/test-enhanced-course-workflows.ts", "security:audit": "tsx scripts/security-audit-tests.ts", "security:validate": "tsx scripts/validate-security-config.ts", "security:scan": "npm audit --audit-level=moderate", "security:check-env": "tsx scripts/check-environment-security.ts", "security:test-all": "tsx scripts/run-security-tests.ts", "security:production-ready": "npm run security:test-all && echo 'Security validation complete'", "deploy:aws": "node deployment/production-env-setup.js", "deploy:infrastructure": "bash deployment/setup-aws-infrastructure.sh"}, "dependencies": {"@anthropic-ai/sdk": "^0.37.0", "@aws-sdk/client-s3": "^3.796.0", "@aws-sdk/s3-request-presigner": "^3.796.0", "@google/generative-ai": "^0.24.0", "@marp-team/marp-cli": "^4.1.2", "@neondatabase/serverless": "^0.10.4", "@sendgrid/mail": "^8.1.5", "@stripe/stripe-js": "^7.4.0", "axios": "^1.9.0", "bcryptjs": "^3.0.2", "connect-pg-simple": "^10.0.0", "cors": "^2.8.5", "cross-fetch": "^4.1.0", "crypto": "^1.0.1", "csv-parse": "^5.6.0", "csv-writer": "^1.6.0", "date-fns": "^3.6.0", "dotenv": "^16.4.7", "drizzle-orm": "^0.39.1", "drizzle-zod": "^0.7.0", "elevenlabs": "^1.56.0", "express": "^4.21.2", "express-session": "^1.18.1", "form-data": "^4.0.3", "google-auth-library": "^10.1.0", "googleapis": "^150.0.1", "jsonwebtoken": "^9.0.2", "jspdf": "^3.0.1", "marked": "^15.0.11", "memoizee": "^0.4.17", "memorystore": "^1.6.7", "multer": "^1.4.5-lts.2", "node-fetch": "^3.3.2", "openai": "^4.104.0", "openid-client": "^6.5.0", "passport": "^0.7.0", "passport-local": "^1.0.0", "resend": "^4.5.1", "sanitize-html": "^2.16.0", "stability-client": "^1.9.0", "stripe": "^18.2.1", "uuid": "^11.1.0", "ws": "^8.18.0", "zod": "^3.23.8", "zod-validation-error": "^3.4.0"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/connect-pg-simple": "^7.0.3", "@types/cors": "^2.8.19", "@types/express": "4.17.21", "@types/express-session": "^1.18.1", "@types/jsonwebtoken": "^9.0.9", "@types/memoizee": "^0.4.12", "@types/multer": "^1.4.12", "@types/node": "20.16.11", "@types/passport": "^1.0.16", "@types/passport-local": "^1.0.38", "@types/sanitize-html": "^2.16.0", "@types/uuid": "^10.0.0", "@types/ws": "^8.5.13", "drizzle-kit": "^0.30.4", "esbuild": "^0.25.0", "tsx": "^4.19.1", "typescript": "5.6.3"}}