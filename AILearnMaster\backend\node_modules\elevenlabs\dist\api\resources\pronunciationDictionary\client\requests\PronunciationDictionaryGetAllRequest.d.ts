/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as ElevenLabs from "../../../../index";
/**
 * @example
 *     {}
 */
export interface PronunciationDictionaryGetAllRequest {
    /**
     * Used for fetching next page. Cursor is returned in the response.
     */
    cursor?: string;
    /**
     * How many pronunciation dictionaries to return at maximum. Can not exceed 100, defaults to 30.
     */
    page_size?: number;
    /**
     * Which field to sort by, one of 'created_at_unix' or 'name'.
     */
    sort?: ElevenLabs.PronunciationDictionaryGetAllRequestSort;
    /**
     * Which direction to sort the voices in. 'ascending' or 'descending'.
     */
    sort_direction?: string;
}
