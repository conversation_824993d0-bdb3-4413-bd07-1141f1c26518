/**
 * This file was auto-generated by <PERSON><PERSON> from our API Definition.
 */
import * as Eleven<PERSON>abs from "../index";
export interface User {
    /** The unique identifier of the user. */
    user_id: string;
    /** Details of the user's subscription. */
    subscription: ElevenLabs.SubscriptionResponse;
    /** Optional additional details about the user's subscription. */
    subscription_extras?: ElevenLabs.SubscriptionExtrasResponseModel;
    /** Whether the user is new. */
    is_new_user: boolean;
    /** The API key of the user. */
    xi_api_key?: string;
    /** Whether the user can use delayed payment methods. */
    can_use_delayed_payment_methods: boolean;
    /** Whether the user's onboarding is completed. */
    is_onboarding_completed: boolean;
    /** Whether the user's onboarding checklist is completed. */
    is_onboarding_checklist_completed: boolean;
    /** First name of the user. */
    first_name?: string;
    /** Whether the user's API key is hashed. */
    is_api_key_hashed?: boolean;
    /** The preview of the user's API key. */
    xi_api_key_preview?: string;
    /** The referral link code of the user. */
    referral_link_code?: string;
    /** The Partnerstack partner default link of the user. */
    partnerstack_partner_default_link?: string;
}
