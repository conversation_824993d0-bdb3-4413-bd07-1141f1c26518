/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as ElevenLabs from "../index";
export type SystemToolConfigOutputParams = ElevenLabs.SystemToolConfigOutputParams.EndCall | ElevenLabs.SystemToolConfigOutputParams.LanguageDetection | ElevenLabs.SystemToolConfigOutputParams.TransferToAgent | ElevenLabs.SystemToolConfigOutputParams.TransferToNumber;
export declare namespace SystemToolConfigOutputParams {
    interface EndCall extends ElevenLabs.EndCallToolConfig {
        system_tool_type: "end_call";
    }
    interface LanguageDetection extends ElevenLabs.LanguageDetectionToolConfig {
        system_tool_type: "language_detection";
    }
    interface TransferToAgent extends ElevenLabs.TransferToAgentToolConfig {
        system_tool_type: "transfer_to_agent";
    }
    interface TransferToNumber extends ElevenLabs.TransferToNumberToolConfig {
        system_tool_type: "transfer_to_number";
    }
}
