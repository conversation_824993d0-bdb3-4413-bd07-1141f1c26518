{"include": ["server/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts"], "compilerOptions": {"incremental": true, "tsBuildInfoFile": "./node_modules/typescript/tsbuildinfo", "noEmit": true, "module": "ESNext", "strict": false, "lib": ["esnext"], "esModuleInterop": true, "skipLibCheck": true, "allowImportingTsExtensions": true, "moduleResolution": "bundler", "baseUrl": ".", "paths": {"@shared/schema": ["./server/shared-schema-alias.ts"]}, "types": ["node"], "downlevelIteration": true, "target": "es2020", "noImplicitAny": false, "allowJs": true}}