/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as ElevenLabs from "../index";
export interface RagConfig {
    enabled?: boolean;
    embedding_model?: ElevenLabs.EmbeddingModelEnum;
    /** Maximum vector distance of retrieved chunks. */
    max_vector_distance?: number;
    /** Maximum total length of document chunks retrieved from RAG. */
    max_documents_length?: number;
}
