/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as ElevenLabs from "../../../../index";
/**
 * @example
 *     {
 *         email: "email"
 *     }
 */
export interface UpdateMemberRequest {
    /** Email of the target user. */
    email: string;
    /** Whether to lock or unlock the user account. */
    is_locked?: boolean;
    /** Role dictating permissions in the workspace. */
    workspace_role?: ElevenLabs.BodyUpdateMemberV1WorkspaceMembersPostWorkspaceRole;
}
