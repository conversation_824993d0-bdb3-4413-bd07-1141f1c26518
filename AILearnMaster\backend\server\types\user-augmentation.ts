// Type augmentation for User interface to resolve TypeScript compilation errors
// This file extends the User type to include missing properties that are accessed throughout the codebase

declare global {
  namespace Express {
    interface User {
      id: number;
      role: string;
      email: string;
      name: string;
      username: string;
      plan?: string;
      subscription?: string;
      emailVerified?: boolean;
      avatarUrl?: string;
      stripeCustomerId?: string;
      stripeSubscriptionId?: string;
      aiCredits?: number;
      preferences?: any;
      googleId?: string;
      authProvider?: string;
    }
  }
}

// Re-export the User type for consistency
export interface UserType {
  id: number;
  role: string;
  email: string;
  name: string;
  username: string;
  plan?: string;
  subscription?: string;
  emailVerified?: boolean;
  avatarUrl?: string;
  stripeCustomerId?: string;
  stripeSubscriptionId?: string;
  aiCredits?: number;
  preferences?: any;
  googleId?: string;
  authProvider?: string;
}

export {};
