import { Router } from 'express';
import { storage } from '../storage';
import { aiQuizGenerator } from '../services/ai-quiz-generator';
import { insertQuizSchema } from '@shared/schema';
import { z } from 'zod';

const router = Router();

// Generate quiz questions using AI
router.post('/ai/generate-quiz-questions', async (req, res) => {
  try {
    const userId = req.user?.id;
    if (!userId) {
      return res.status(401).json({ message: 'Not authenticated' });
    }

    const { content, questionCount, difficulty, questionTypes, moduleTitle, lessonTitle } = req.body;

    if (!content) {
      return res.status(400).json({ message: 'Content is required for question generation' });
    }

    // Validate input parameters
    const validationSchema = z.object({
      content: z.string().min(50, 'Content must be at least 50 characters'),
      questionCount: z.number().min(1).max(20),
      difficulty: z.enum(['easy', 'medium', 'hard', 'mixed']),
      questionTypes: z.array(z.enum(['multiple-choice', 'true-false', 'short-answer', 'essay'])).min(1),
      moduleTitle: z.string().optional(),
      lessonTitle: z.string().optional(),
    });

    const validatedData = validationSchema.parse({
      content,
      questionCount,
      difficulty,
      questionTypes,
      moduleTitle,
      lessonTitle,
    });

    console.log(`Generating ${questionCount} questions for user ${userId}`);

    const result = await aiQuizGenerator.generateQuestions(validatedData as {
      content: string;
      questionCount: number;
      difficulty: 'easy' | 'medium' | 'hard' | 'mixed';
      questionTypes: string[];
      moduleTitle?: string;
      lessonTitle?: string;
    });

    res.json(result);
  } catch (error) {
    console.error('Error generating quiz questions:', error);
    
    if (error instanceof z.ZodError) {
      return res.status(400).json({ 
        message: 'Invalid input parameters',
        errors: error.errors 
      });
    }

    res.status(500).json({ 
      message: 'Failed to generate quiz questions',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Analyze content for quiz generation insights
router.post('/ai/analyze-content', async (req, res) => {
  try {
    const userId = req.user?.id;
    if (!userId) {
      return res.status(401).json({ message: 'Not authenticated' });
    }

    const { content } = req.body;

    if (!content || content.length < 50) {
      return res.status(400).json({ message: 'Content must be at least 50 characters long' });
    }

    const analysis = await aiQuizGenerator.analyzeContent(content);

    res.json(analysis);
  } catch (error) {
    console.error('Error analyzing content:', error);
    res.status(500).json({ 
      message: 'Failed to analyze content',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Create a new quiz
router.post('/quizzes', async (req, res) => {
  try {
    const userId = req.user?.id;
    if (!userId) {
      return res.status(401).json({ message: 'Not authenticated' });
    }

    const quizData = {
      ...req.body,
      userId,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    // Validate quiz data
    const validatedQuiz = insertQuizSchema.parse(quizData);
    
    const savedQuiz = await storage.createQuiz(validatedQuiz);
    res.json(savedQuiz);
  } catch (error) {
    console.error('Error creating quiz:', error);
    
    if (error instanceof z.ZodError) {
      return res.status(400).json({ 
        message: 'Invalid quiz data',
        errors: error.errors 
      });
    }

    res.status(500).json({ 
      message: 'Failed to create quiz',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Get quiz by ID
router.get('/quizzes/:id', async (req, res) => {
  try {
    const userId = req.user?.id;
    if (!userId) {
      return res.status(401).json({ message: 'Not authenticated' });
    }

    const { id } = req.params;
    const quiz = await storage.getQuiz(parseInt(id));
    
    if (!quiz) {
      return res.status(404).json({ message: 'Quiz not found' });
    }

    // Check if user owns this quiz or has access to it
    if (quiz.userId !== userId) {
      return res.status(403).json({ message: 'Access denied' });
    }

    res.json(quiz);
  } catch (error) {
    console.error('Error getting quiz:', error);
    res.status(500).json({ message: 'Failed to get quiz' });
  }
});

// Get all quizzes for a course
router.get('/courses/:courseId/quizzes', async (req, res) => {
  try {
    const userId = req.user?.id;
    if (!userId) {
      return res.status(401).json({ message: 'Not authenticated' });
    }

    const { courseId } = req.params;
    const quizzes = await storage.getQuizzesByCourseId(parseInt(courseId));
    
    // Filter quizzes to only show those owned by the user
    const userQuizzes = quizzes.filter(quiz => quiz.userId === userId);

    res.json(userQuizzes);
  } catch (error) {
    console.error('Error getting course quizzes:', error);
    res.status(500).json({ message: 'Failed to get course quizzes' });
  }
});

// Get quizzes for a specific module
router.get('/modules/:moduleId/quizzes', async (req, res) => {
  try {
    const userId = req.user?.id;
    if (!userId) {
      return res.status(401).json({ message: 'Not authenticated' });
    }

    const { moduleId } = req.params;
    const quizzes = await storage.getQuizzesByModuleId(parseInt(moduleId));
    
    // Filter quizzes to only show those owned by the user
    const userQuizzes = quizzes.filter(quiz => quiz.userId === userId);

    res.json(userQuizzes);
  } catch (error) {
    console.error('Error getting module quizzes:', error);
    res.status(500).json({ message: 'Failed to get module quizzes' });
  }
});

// Update quiz
router.put('/quizzes/:id', async (req, res) => {
  try {
    const userId = req.user?.id;
    if (!userId) {
      return res.status(401).json({ message: 'Not authenticated' });
    }

    const { id } = req.params;
    const quizId = parseInt(id);

    // Check if quiz exists and user owns it
    const existingQuiz = await storage.getQuiz(quizId);
    if (!existingQuiz) {
      return res.status(404).json({ message: 'Quiz not found' });
    }

    if (existingQuiz.userId !== userId) {
      return res.status(403).json({ message: 'Access denied' });
    }

    const updateData = {
      ...req.body,
      updatedAt: new Date(),
    };

    const updatedQuiz = await storage.updateQuiz(quizId, updateData);
    
    if (!updatedQuiz) {
      return res.status(404).json({ message: 'Quiz not found' });
    }

    res.json(updatedQuiz);
  } catch (error) {
    console.error('Error updating quiz:', error);
    res.status(500).json({ message: 'Failed to update quiz' });
  }
});

// Delete quiz
router.delete('/quizzes/:id', async (req, res) => {
  try {
    const userId = req.user?.id;
    if (!userId) {
      return res.status(401).json({ message: 'Not authenticated' });
    }

    const { id } = req.params;
    const quizId = parseInt(id);

    // Check if quiz exists and user owns it
    const existingQuiz = await storage.getQuiz(quizId);
    if (!existingQuiz) {
      return res.status(404).json({ message: 'Quiz not found' });
    }

    if (existingQuiz.userId !== userId) {
      return res.status(403).json({ message: 'Access denied' });
    }

    const deleted = await storage.deleteQuiz(quizId);
    
    if (!deleted) {
      return res.status(404).json({ message: 'Quiz not found' });
    }

    res.json({ success: true });
  } catch (error) {
    console.error('Error deleting quiz:', error);
    res.status(500).json({ message: 'Failed to delete quiz' });
  }
});

// Create quiz questions
router.post('/quizzes/:quizId/questions', async (req, res) => {
  try {
    const userId = req.user?.id;
    if (!userId) {
      return res.status(401).json({ message: 'Not authenticated' });
    }

    const { quizId } = req.params;
    const quiz = await storage.getQuiz(parseInt(quizId));
    
    if (!quiz || quiz.userId !== userId) {
      return res.status(404).json({ message: 'Quiz not found or access denied' });
    }

    const questionData = {
      ...req.body,
      quizId: parseInt(quizId),
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    const question = await storage.createQuizQuestion(questionData);
    res.json(question);
  } catch (error) {
    console.error('Error creating quiz question:', error);
    res.status(500).json({ message: 'Failed to create quiz question' });
  }
});

// Get quiz questions
router.get('/quizzes/:quizId/questions', async (req, res) => {
  try {
    const userId = req.user?.id;
    if (!userId) {
      return res.status(401).json({ message: 'Not authenticated' });
    }

    const { quizId } = req.params;
    const quiz = await storage.getQuiz(parseInt(quizId));
    
    if (!quiz || quiz.userId !== userId) {
      return res.status(404).json({ message: 'Quiz not found or access denied' });
    }

    const questions = await storage.getQuizQuestions(parseInt(quizId));
    res.json(questions);
  } catch (error) {
    console.error('Error getting quiz questions:', error);
    res.status(500).json({ message: 'Failed to get quiz questions' });
  }
});

// Submit quiz attempt
router.post('/quizzes/:quizId/attempts', async (req, res) => {
  try {
    const userId = req.user?.id;
    if (!userId) {
      return res.status(401).json({ message: 'Not authenticated' });
    }

    const { quizId } = req.params;
    const { answers, timeSpent } = req.body;

    const quiz = await storage.getQuiz(parseInt(quizId));
    if (!quiz) {
      return res.status(404).json({ message: 'Quiz not found' });
    }

    const questions = await storage.getQuizQuestions(parseInt(quizId));
    
    // Calculate score
    let correctAnswers = 0;
    const totalQuestions = questions.length;

    for (const question of questions) {
      const userAnswer = answers[question.id];
      if (userAnswer === question.correctAnswer) {
        correctAnswers++;
      }
    }

    const score = totalQuestions > 0 ? Math.round((correctAnswers / totalQuestions) * 100) : 0;
    const passed = score >= (quiz.passingScore || 70);

    const attemptData = {
      userId,
      quizId: parseInt(quizId),
      answers: JSON.stringify(answers),
      score,
      passed,
      timeSpent: timeSpent || 0,
      completedAt: new Date(),
      createdAt: new Date(),
    };

    const attempt = await storage.createQuizAttempt(attemptData);
    
    res.json({
      ...attempt,
      correctAnswers,
      totalQuestions,
      questions: quiz.showCorrectAnswers ? questions : undefined,
    });
  } catch (error) {
    console.error('Error submitting quiz attempt:', error);
    res.status(500).json({ message: 'Failed to submit quiz attempt' });
  }
});

// Get quiz attempts for a user
router.get('/quizzes/:quizId/attempts', async (req, res) => {
  try {
    const userId = req.user?.id;
    if (!userId) {
      return res.status(401).json({ message: 'Not authenticated' });
    }

    const { quizId } = req.params;
    const attempts = await storage.getQuizAttempts(parseInt(quizId), userId);
    
    res.json(attempts);
  } catch (error) {
    console.error('Error getting quiz attempts:', error);
    res.status(500).json({ message: 'Failed to get quiz attempts' });
  }
});

export default router;