/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as ElevenLabs from "../index";
/**
 * A webhook tool is a tool that calls an external webhook from our server
 */
export interface WebhookToolConfigInput {
    id?: string;
    name: string;
    description: string;
    /** The maximum time in seconds to wait for the tool call to complete. Must be between 5 and 120 seconds (inclusive). */
    response_timeout_secs?: number;
    /** The schema for the outgoing webhoook, including parameters and URL specification */
    api_schema: ElevenLabs.WebhookToolApiSchemaConfigInput;
    /** Configuration for dynamic variables */
    dynamic_variables?: ElevenLabs.DynamicVariablesConfig;
}
