// Temporary alias for shared schema to resolve import issues
// This file provides fallback types and exports when the shared schema is not available

// Basic type definitions to prevent compilation errors
export interface User {
  id: number;
  email: string;
  name: string;
  username: string;
  role: string;
  plan?: string;
  subscription?: string;
  emailVerified?: boolean;
  avatarUrl?: string;
  stripeCustomerId?: string;
  stripeSubscriptionId?: string;
  aiCredits?: number;
  preferences?: any;
  googleId?: string;
  authProvider?: string;
}

export interface Course {
  id: number;
  title: string;
  description?: string;
  userId: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface Module {
  id: number;
  title: string;
  description?: string;
  courseId: number;
  order: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface Lesson {
  id: number;
  title: string;
  content?: string;
  moduleId: number;
  order: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface MediaLibrary {
  id: number;
  filename: string;
  originalName: string;
  mimeType: string;
  size: number;
  userId: number;
  createdAt: Date;
}

export interface AiCredits {
  id: number;
  userId: number;
  credits: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface AiUsageHistory {
  id: number;
  userId: number;
  action: string;
  creditsUsed: number;
  createdAt: Date;
}

export interface AiGeneratedImages {
  id: number;
  userId: number;
  prompt: string;
  imageUrl: string;
  createdAt: Date;
}

export interface ChatSessions {
  id: number;
  userId: number;
  title?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface ChatMessages {
  id: number;
  sessionId: number;
  role: string;
  content: string;
  createdAt: Date;
}

// Schema objects (simplified)
export const users = {
  findFirst: () => Promise.resolve(null),
  findMany: () => Promise.resolve([])
} as any;
export const courses = {
  findFirst: () => Promise.resolve(null),
  findMany: () => Promise.resolve([])
} as any;
export const modules = {
  findFirst: () => Promise.resolve(null),
  findMany: () => Promise.resolve([])
} as any;
export const lessons = {
  findFirst: () => Promise.resolve(null),
  findMany: () => Promise.resolve([])
} as any;
export const mediaLibrary = {} as any;
export const aiCredits = {} as any;
export const aiUsageHistory = {} as any;
export const aiGeneratedImages = {} as any;
export const chatSessions = {
  findFirst: () => Promise.resolve(null),
  findMany: () => Promise.resolve([])
} as any;
export const chatMessages = {} as any;

// Insert schemas (simplified)
export const insertUserSchema = {} as any;
export const insertCourseDraftSchema = {} as any;
export const insertChatSessionSchema = {} as any;
export const insertChatMessageSchema = {} as any;
export const insertQuizSchema = {} as any;
export const insertEmailCampaignSchema = {} as any;
export const insertSubscriberSchema = {} as any;
export const insertSubscriberListSchema = {} as any;
export const insertMicroLearningSegmentSchema = {} as any;
export const insertMicroLearningKnowledgeCheckSchema = {} as any;
export const insertMicroLearningUserProgressSchema = {} as any;
export const insertUserKnowledgeCheckResponseSchema = {} as any;

// Additional table exports
export const emailCampaigns = {} as any;
export const subscriberLists = {} as any;
export const subscribers = {} as any;
export const emailTemplates = {} as any;
export const emailSettings = {} as any;
export const ttsRecords = {} as any;
export const generatedVideos = {} as any;
export const miniCourses = {} as any;
export const userApiKeys = {} as any;
export const landingPages = {} as any;
export const landingPageVisits = {} as any;
export const microLearningSegments = {} as any;
export const microLearningKnowledgeChecks = {} as any;
export const microLearningUserProgress = {} as any;
export const userKnowledgeCheckResponses = {} as any;
export const courseDrafts = {} as any;
export const integrations = {} as any;
export const userStats = {} as any;
export const templates = {} as any;
export const templateHistory = {} as any;
export const teams = {} as any;
export const teamMembers = {} as any;
export const courseCollaborators = {} as any;
export const teamCourses = {} as any;
export const courseAnalytics = {} as any;
export const lessonAnalytics = {} as any;
export const userCourseProgress = {} as any;
export const userLessonProgressRecord = {} as any;
export const analyticsEvents = {} as any;
export const publishing = {} as any;

// Type exports
export type InsertUser = any;
export type InsertCourse = any;
export type InsertModule = any;
export type InsertLesson = any;
export type Media = any;
export type InsertMedia = any;
export type Integration = any;
export type InsertIntegration = any;
export type UserStats = any;
export type InsertUserStats = any;
export type Template = any;
export type TemplateHistory = any;
export type InsertTemplateHistory = any;
export type Team = any;
export type InsertTeam = any;
export type TeamMember = any;
export type InsertTeamMember = any;
export type CourseCollaborator = any;
export type InsertCourseCollaborator = any;
export type TeamCourse = any;
export type InsertTeamCourse = any;
export type CourseAnalytics = any;
export type InsertCourseAnalytics = any;
export type LessonAnalytics = any;
export type InsertLessonAnalytics = any;
export type UserCourseProgress = any;
export type InsertUserCourseProgress = any;
export type UserLessonProgress = any;
export type InsertUserLessonProgress = any;
export type AnalyticsEvent = any;
export type InsertAnalyticsEvent = any;
export type Publishing = any;
export type InsertPublishing = any;
export type TTSRecord = any;
export type InsertTTSRecord = any;
export type TTSResponse = any;
export type AiGeneratedImage = any;
export type InsertAiGeneratedImage = any;
export type GeneratedVideo = any;
export type InsertGeneratedVideo = any;
export type MiniCourse = any;
export type InsertMiniCourse = any;
export type UserApiKey = any;
export type InsertUserApiKey = any;
export type LandingPage = any;
export type InsertLandingPage = any;
export type LandingPageVisit = any;
export type InsertLandingPageVisit = any;
export type MicroLearningSegment = any;
export type InsertMicroLearningSegment = any;
export type MicroLearningKnowledgeCheck = any;
export type InsertMicroLearningKnowledgeCheck = any;
export type MicroLearningUserProgress = any;
export type InsertMicroLearningUserProgress = any;
export type UserKnowledgeCheckResponse = any;
export type InsertUserKnowledgeCheckResponse = any;
export type CourseDraft = any;
export type InsertCourseDraft = any;
export type EmailTemplate = any;
export type InsertEmailTemplate = any;

// Export everything that might be imported
export * from './types/user-augmentation';
