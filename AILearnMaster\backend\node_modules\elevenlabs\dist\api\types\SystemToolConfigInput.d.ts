/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as ElevenLabs from "../index";
/**
 * A system tool is a tool that is used to call a system method in the server
 */
export interface SystemToolConfigInput {
    id?: string;
    name: string;
    description: string;
    /** The maximum time in seconds to wait for the tool call to complete. */
    response_timeout_secs?: number;
    params: ElevenLabs.SystemToolConfigInputParams;
}
