/**
 * Secure Configuration Manager
 * Prevents runtime modification of environment variables and provides secure API key management
 */

import crypto from 'crypto';

// Configuration interface
interface SecureConfig {
  [key: string]: string | undefined;
}

// Encrypted configuration store
class SecureConfigManager {
  private config: SecureConfig = {};
  private encryptionKey: string;
  private isLocked: boolean = false;

  constructor() {
    // Generate encryption key from environment or create one
    this.encryptionKey = process.env.CONFIG_ENCRYPTION_KEY || this.generateEncryptionKey();
    
    // Load initial configuration from environment
    this.loadFromEnvironment();
    
    // Lock configuration in production
    if (process.env.NODE_ENV === 'production') {
      this.lockConfiguration();
    }
  }

  /**
   * Generate a secure encryption key
   */
  private generateEncryptionKey(): string {
    return crypto.randomBytes(32).toString('hex');
  }

  /**
   * Load configuration from environment variables
   */
  private loadFromEnvironment(): void {
    const envVars = [
      'OPENAI_API_KEY',
      'ANTHROPIC_API_KEY',
      'GOOGLE_API_KEY',
      'ELEVENLABS_API_KEY',
      'STABILITY_API_KEY',
      'RUNPOD_API_KEY',
      'AWS_ACCESS_KEY_ID',
      'AWS_SECRET_ACCESS_KEY',
      'AWS_REGION',
      'AWS_S3_BUCKET',
      'AWS_CLOUDFRONT_DOMAIN',
      'RUNPOD_KOKORO_ENDPOINT',
      'RUNPOD_COQUI_ENDPOINT',
      'RUNPOD_SADTALKER_ENDPOINT',
      'RUNPOD_MARP_ENDPOINT',
      'STRIPE_SECRET_KEY',
      'STRIPE_PUBLISHABLE_KEY',
      'DATABASE_URL',
      'SESSION_SECRET'
    ];

    envVars.forEach(key => {
      if (process.env[key]) {
        this.config[key] = process.env[key];
      }
    });
  }

  /**
   * Lock configuration to prevent further modifications
   */
  private lockConfiguration(): void {
    this.isLocked = true;
    console.log('🔒 Configuration locked for production');
  }

  /**
   * Encrypt sensitive data using AES-256-GCM
   */
  private encrypt(text: string): string {
    try {
      const iv = crypto.randomBytes(16);
      const cipher = crypto.createCipher('aes-256-cbc', this.encryptionKey);
      let encrypted = cipher.update(text, 'utf8', 'hex');
      encrypted += cipher.final('hex');
      return encrypted;
    } catch (error) {
      console.error('Encryption error:', error);
      throw new Error('Failed to encrypt data');
    }
  }

  /**
   * Decrypt sensitive data using AES-256-GCM
   */
  private decrypt(encryptedText: string): string {
    try {
      const decipher = crypto.createDecipher('aes-256-cbc', this.encryptionKey);
      let decrypted = decipher.update(encryptedText, 'hex', 'utf8');
      decrypted += decipher.final('utf8');
      return decrypted;
    } catch (error) {
      console.error('Decryption error:', error);
      throw new Error('Failed to decrypt data');
    }
  }

  /**
   * Securely set configuration value
   */
  public setConfig(key: string, value: string, userId?: number): boolean {
    if (this.isLocked) {
      console.warn(`🚨 Attempt to modify locked configuration: ${key}`);
      return false;
    }

    // Log configuration changes for security audit
    console.log(`🔧 Configuration updated: ${key} by user ${userId || 'system'}`);
    
    // Store encrypted value for sensitive keys
    const sensitiveKeys = ['API_KEY', 'SECRET', 'PASSWORD', 'TOKEN'];
    const isSensitive = sensitiveKeys.some(pattern => key.includes(pattern));
    
    if (isSensitive) {
      this.config[key] = this.encrypt(value);
    } else {
      this.config[key] = value;
    }

    return true;
  }

  /**
   * Securely get configuration value
   */
  public getConfig(key: string): string | undefined {
    const value = this.config[key];
    
    if (!value) {
      return undefined;
    }

    // Decrypt if it's a sensitive value
    const sensitiveKeys = ['API_KEY', 'SECRET', 'PASSWORD', 'TOKEN'];
    const isSensitive = sensitiveKeys.some(pattern => key.includes(pattern));
    
    if (isSensitive) {
      try {
        return this.decrypt(value);
      } catch (error) {
        console.error(`Failed to decrypt config value for ${key}:`, error);
        return undefined;
      }
    }

    return value;
  }

  /**
   * Get masked configuration value for display
   */
  public getMaskedConfig(key: string): string {
    const value = this.getConfig(key);
    
    if (!value) {
      return 'Not configured';
    }

    // Mask sensitive values
    const sensitiveKeys = ['API_KEY', 'SECRET', 'PASSWORD', 'TOKEN'];
    const isSensitive = sensitiveKeys.some(pattern => key.includes(pattern));
    
    if (isSensitive) {
      return '••••••••••••••••';
    }

    return value;
  }

  /**
   * Validate configuration completeness
   */
  public validateConfiguration(): {
    isValid: boolean;
    missingKeys: string[];
    issues: string[];
  } {
    const requiredKeys = [
      'DATABASE_URL',
      'SESSION_SECRET'
    ];

    const recommendedKeys = [
      'OPENAI_API_KEY',
      'GOOGLE_API_KEY'
    ];

    const missingKeys: string[] = [];
    const issues: string[] = [];

    // Check required keys
    requiredKeys.forEach(key => {
      if (!this.getConfig(key)) {
        missingKeys.push(key);
      }
    });

    // Check recommended keys
    recommendedKeys.forEach(key => {
      if (!this.getConfig(key)) {
        issues.push(`Recommended configuration missing: ${key}`);
      }
    });

    // Validate session secret strength
    const sessionSecret = this.getConfig('SESSION_SECRET');
    if (sessionSecret && sessionSecret.length < 32) {
      issues.push('SESSION_SECRET should be at least 32 characters');
    }

    return {
      isValid: missingKeys.length === 0,
      missingKeys,
      issues
    };
  }

  /**
   * Get configuration summary for monitoring
   */
  public getConfigurationSummary(): {
    configuredKeys: string[];
    missingRecommended: string[];
    securityScore: number;
  } {
    const allKeys = Object.keys(this.config);
    const validation = this.validateConfiguration();
    
    // Calculate security score
    let securityScore = 100;
    securityScore -= validation.missingKeys.length * 20; // -20 for each missing required key
    securityScore -= validation.issues.length * 5; // -5 for each issue
    securityScore = Math.max(0, securityScore);

    return {
      configuredKeys: allKeys,
      missingRecommended: validation.issues,
      securityScore
    };
  }

  /**
   * Clear sensitive configuration (for security)
   */
  public clearSensitiveConfig(): void {
    const sensitiveKeys = Object.keys(this.config).filter(key => 
      ['API_KEY', 'SECRET', 'PASSWORD', 'TOKEN'].some(pattern => key.includes(pattern))
    );

    sensitiveKeys.forEach(key => {
      delete this.config[key];
    });

    console.log(`🧹 Cleared ${sensitiveKeys.length} sensitive configuration keys`);
  }
}

// Create singleton instance
export const secureConfig = new SecureConfigManager();

/**
 * Safe configuration getter (replacement for process.env access)
 */
export const getSecureConfig = (key: string): string | undefined => {
  return secureConfig.getConfig(key);
};

/**
 * Safe configuration setter (replacement for process.env modification)
 */
export const setSecureConfig = (key: string, value: string, userId?: number): boolean => {
  return secureConfig.setConfig(key, value, userId);
};

/**
 * Get masked configuration for display
 */
export const getMaskedConfig = (key: string): string => {
  return secureConfig.getMaskedConfig(key);
};

/**
 * Validate all configuration
 */
export const validateSecureConfig = () => {
  return secureConfig.validateConfiguration();
};

/**
 * Get configuration summary
 */
export const getConfigSummary = () => {
  return secureConfig.getConfigurationSummary();
};