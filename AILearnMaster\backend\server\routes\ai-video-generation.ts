import express, { Request, Response } from 'express';
import { v4 as uuidv4 } from 'uuid';
import { storage } from '../storage';
import { z } from 'zod';
import OpenAI from 'openai';
import path from 'path';
import fs from 'fs';

const router = express.Router();

// Initialize OpenAI
if (!process.env.OPENAI_API_KEY) {
  console.warn('OPENAI_API_KEY not found. AI features will not work correctly.');
}

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

// Credit costs for different operations
const CREDIT_COSTS = {
  SCRIPT_ANALYSIS: 20,
  SCENE_GENERATION: 30,
  VIDEO_GENERATION: 150
};

// Scene validation schema
const sceneSchema = z.object({
  id: z.string(),
  description: z.string(),
  keyPoints: z.array(z.string()),
  audioTimestamp: z.string(),
  imagePrompt: z.string(),
  duration: z.number(),
  imageUrl: z.string().nullable().optional(),
});

// Script analysis request schema
const analyzeScriptSchema = z.object({
  script: z.string().min(10)
});

// Scene generation request schema
const generateScenesSchema = z.object({
  scenes: z.array(sceneSchema),
  style: z.string().optional()
});

// Video generation request schema
const generateVideoSchema = z.object({
  title: z.string(),
  scenes: z.array(sceneSchema),
  audioUrl: z.string(),
  style: z.string().optional()
});

// Analyze script for animation
router.post('/ai/analyze-script-for-animation', async (req: Request, res: Response) => {
  if (!req.session?.userId) {
    return res.status(401).json({ error: 'Not authenticated' });
  }

  try {
    const result = analyzeScriptSchema.safeParse(req.body);
    if (!result.success) {
      return res.status(400).json({ 
        error: 'Invalid request data',
        details: result.error.format()
      });
    }

    const { script } = result.data;
    const userId = req.session.userId;

    // Check if user has enough credits
    const userStats = await storage.getUserStats(userId);
    if (!userStats || userStats.aiCredits < CREDIT_COSTS.SCRIPT_ANALYSIS) {
      return res.status(400).json({
        error: 'Insufficient credits',
        creditsNeeded: CREDIT_COSTS.SCRIPT_ANALYSIS,
        creditsAvailable: userStats ? userStats.aiCredits : 0
      });
    }

    // Analyze script with OpenAI
    const response = await openai.chat.completions.create({
      model: "gpt-4o", // the newest OpenAI model is "gpt-4o" which was released May 13, 2024. do not change this unless explicitly requested by the user
      messages: [
        {
          role: "system",
          content: "You are an expert video producer that breaks down scripts into scenes for animation. Analyze the script and break it down into distinct scenes with timestamps. For each scene, extract key points and create a prompt for image generation."
        },
        {
          role: "user",
          content: `Analyze this script and break it down into scenes for animation: ${script}`
        }
      ],
      response_format: { type: "json_object" },
    });

    // Deduct credits
    await storage.deductUserCredits(userId, CREDIT_COSTS.SCRIPT_ANALYSIS);

    // Record credit transaction
    await storage.recordCreditTransaction({
      userId,
      amount: -CREDIT_COSTS.SCRIPT_ANALYSIS,
      type: 'debit',
      description: `Script analysis for animation video`,
    });

    // Parse the response
    const content = response.choices[0].message.content;
    const analysis = JSON.parse(content);

    // Generate ID for each scene
    if (analysis.scenes) {
      analysis.scenes = analysis.scenes.map((scene: any) => ({
        ...scene,
        id: uuidv4()
      }));
    }

    return res.status(200).json({
      success: true,
      analysis
    });
  } catch (error) {
    console.error('Error analyzing script:', error);
    return res.status(500).json({ 
      error: 'Failed to analyze script',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Generate animation scenes
router.post('/ai/generate-animation-scenes', async (req: Request, res: Response) => {
  if (!req.session?.userId) {
    return res.status(401).json({ error: 'Not authenticated' });
  }

  try {
    const result = generateScenesSchema.safeParse(req.body);
    if (!result.success) {
      return res.status(400).json({ 
        error: 'Invalid request data',
        details: result.error.format()
      });
    }

    const { scenes, style } = result.data;
    const userId = req.session.userId;

    // Check if user has enough credits
    const userStats = await storage.getUserStats(userId);
    if (!userStats || userStats.aiCredits < CREDIT_COSTS.SCENE_GENERATION) {
      return res.status(400).json({
        error: 'Insufficient credits',
        creditsNeeded: CREDIT_COSTS.SCENE_GENERATION,
        creditsAvailable: userStats ? userStats.aiCredits : 0
      });
    }

    // Process scenes and generate image prompts
    const updatedScenes = await Promise.all(scenes.map(async (scene) => {
      try {
        // For development purposes, we're just returning a URL to a placeholder image
        // In production, this would call the Stability API to generate an image
        // TODO: Replace with actual image generation
        const stylePrefix = style ? `${style} style,` : '';
        const imagePrompt = `${stylePrefix} ${scene.imagePrompt}`;

        // Mock image URL
        const imageUrl = `/placeholder-images/scene-${Math.floor(Math.random() * 5) + 1}.jpg`;

        return {
          ...scene,
          imageUrl,
          imagePrompt
        };
      } catch (error) {
        console.error(`Error generating image for scene ${scene.id}:`, error);
        return scene;
      }
    }));

    // Deduct credits
    await storage.deductUserCredits(userId, CREDIT_COSTS.SCENE_GENERATION);

    // Record credit transaction
    await storage.recordCreditTransaction({
      userId,
      amount: -CREDIT_COSTS.SCENE_GENERATION,
      type: 'debit',
      description: `Scene generation for animation video (${scenes.length} scenes)`,
    });

    return res.status(200).json({
      success: true,
      scenes: updatedScenes
    });
  } catch (error) {
    console.error('Error generating scenes:', error);
    return res.status(500).json({ 
      error: 'Failed to generate scenes',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Generate animated video
router.post('/ai/generate-animated-video', async (req: Request, res: Response) => {
  if (!req.session?.userId) {
    return res.status(401).json({ error: 'Not authenticated' });
  }

  try {
    const result = generateVideoSchema.safeParse(req.body);
    if (!result.success) {
      return res.status(400).json({ 
        error: 'Invalid request data',
        details: result.error.format()
      });
    }

    const { title, scenes, audioUrl, style } = result.data;
    const userId = req.session.userId;

    // Check if user has enough credits
    const userStats = await storage.getUserStats(userId);
    if (!userStats || userStats.aiCredits < CREDIT_COSTS.VIDEO_GENERATION) {
      return res.status(400).json({
        error: 'Insufficient credits',
        creditsNeeded: CREDIT_COSTS.VIDEO_GENERATION,
        creditsAvailable: userStats ? userStats.aiCredits : 0
      });
    }

    // Create a job for video generation
    const jobId = uuidv4();

    // In a real implementation, we would start a background task here
    // For now, we'll just save the job info and return the ID
    const videoJob = {
      id: jobId,
      userId,
      title,
      scenes,
      audioUrl,
      style,
      createdAt: new Date(),
      status: 'processing',
      estimatedCompletionTime: 120, // 2 minutes
    };

    // Store job information (using videoPending as a placeholder)
    // In a real implementation, this would be stored in a database
    await storage.createVideoPending({
      jobId: videoJob.id,
      userId: videoJob.userId,
      status: videoJob.status,
      progress: 0
    });

    // Deduct credits
    await storage.deductUserCredits(userId, CREDIT_COSTS.VIDEO_GENERATION);

    // Record credit transaction
    await storage.recordCreditTransaction({
      userId,
      amount: -CREDIT_COSTS.VIDEO_GENERATION,
      type: 'debit',
      description: `Video generation for animation "${title}" (${scenes.length} scenes)`,
    });

    // In a real implementation, this would trigger an asynchronous video generation process
    // For demo purposes, we'll simulate completion after a delay
    setTimeout(async () => {
      try {
        // Update job status with a completed video URL
        // In a real implementation, this would be the actual generated video
        const videoUrl = '/placeholder-videos/animation-sample.mp4';

        await storage.updateVideoPending(jobId, {
          status: 'completed',
          videoUrl,
          completedAt: new Date().toISOString()
        });
      } catch (error) {
        console.error(`Error completing video job ${jobId}:`, error);

        await storage.updateVideoPending(jobId, {
          status: 'failed',
          error: error instanceof Error ? error.message : 'Unknown error',
        });
      }
    }, 5000); // Simulate 5 second processing time for demo

    return res.status(200).json({
      success: true,
      jobId
    });
  } catch (error) {
    console.error('Error generating video:', error);
    return res.status(500).json({ 
      error: 'Failed to generate video',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Check video generation status
router.get('/ai/video-status/:id', async (req: Request, res: Response) => {
  if (!req.session?.userId) {
    return res.status(401).json({ error: 'Not authenticated' });
  }

  try {
    const jobId = req.params.id;
    const userId = req.session.userId;

    // Get video job status
    const videoJob = await storage.getMediaBySourceId(jobId); // Modified line

    if (!videoJob) {
      return res.status(404).json({ error: 'Video job not found' });
    }

    // Check if the job belongs to the requesting user
    if (videoJob.userId !== userId) {
      return res.status(403).json({ error: 'Access denied' });
    }

    return res.status(200).json({
      id: videoJob.id,
      status: videoJob.status,
      createdAt: videoJob.createdAt,
      videoUrl: videoJob.videoUrl,
      error: videoJob.error,
      estimatedCompletionTime: videoJob.estimatedCompletionTime,
      progress: videoJob.progress
    });
  } catch (error) {
    console.error('Error checking video status:', error);
    return res.status(500).json({ 
      error: 'Failed to check video status',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

export default router;