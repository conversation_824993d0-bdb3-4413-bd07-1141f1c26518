/**
 * This file was auto-generated by <PERSON>rn from our API Definition.
 */
import * as Eleven<PERSON>abs from "../index";
/**
 * The type of tool
 */
export type PromptAgentInputToolsItem = ElevenLabs.PromptAgentInputToolsItem.Client | ElevenLabs.PromptAgentInputToolsItem.Mcp | ElevenLabs.PromptAgentInputToolsItem.System | ElevenLabs.PromptAgentInputToolsItem.Webhook;
export declare namespace PromptAgentInputToolsItem {
    interface Client extends ElevenLabs.ClientToolConfigInput {
        type: "client";
    }
    interface Mcp extends ElevenLabs.McpToolConfigInput {
        type: "mcp";
    }
    interface System extends ElevenLabs.SystemToolConfigInput {
        type: "system";
    }
    interface Webhook extends ElevenLabs.WebhookToolConfigInput {
        type: "webhook";
    }
}
