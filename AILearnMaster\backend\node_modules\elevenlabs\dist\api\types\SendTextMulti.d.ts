/**
 * This file was auto-generated by Fern from our API Definition.
 */
/**
 * Payload to send text for synthesis to an existing context.
 */
export interface SendTextMulti {
    /** Text to synthesize. Should end with a single space. */
    text: string;
    /** The target context_id for this text. */
    context_id?: string;
    /** If true, flushes the audio buffer for the specified context. If false, the text will be appended to the buffer to be generated. */
    flush?: boolean;
}
