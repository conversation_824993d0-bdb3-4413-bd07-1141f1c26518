/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as ElevenLabs from "../index";
export interface PronunciationDictionaryVersionResponseModel {
    version_id: string;
    version_rules_num: number;
    pronunciation_dictionary_id: string;
    dictionary_name: string;
    version_name: string;
    permission_on_resource?: ElevenLabs.PronunciationDictionaryVersionResponseModelPermissionOnResource;
    created_by: string;
    creation_time_unix: number;
    archived_time_unix?: number;
}
