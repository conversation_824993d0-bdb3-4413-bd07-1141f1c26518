import { Router, Request, Response } from 'express';
import { z } from 'zod';
import { eq, count, and, sql } from 'drizzle-orm';
import { db } from '../db';
import { subscribers, subscriberLists, insertSubscriberSchema, insertSubscriberListSchema } from '@shared/schema';
import multer from 'multer';
import { createObjectCsvWriter } from 'csv-writer';
import fs from 'fs';
import { parse } from 'csv-parse';
import path from 'path';
import { storage } from '../storage';

const router = Router();
const upload = multer({ dest: 'uploads/' });

// Validation schema for creating/updating a subscriber
const subscriberSchema = z.object({
  email: z.string().email('Valid email is required'),
  name: z.string().optional(),
  status: z.enum(['active', 'inactive', 'unsubscribed']).optional(),
  source: z.string().optional(),
  listId: z.number().optional(),
  metadata: z.record(z.any()).optional(),
});

// Validation schema for creating a subscriber list
const subscriberListSchema = z.object({
  name: z.string().min(1, 'List name is required'),
  description: z.string().optional(),
});

// Get subscriber count statistics
router.get('/stats', async (req: Request, res: Response) => {
  try {
    if (!req.session?.userId) {
      return res.status(401).json({ message: 'Not authenticated' });
    }

    // Get total subscriber count
    const [totalResult] = await db
      .select({ count: count() })
      .from(subscribers)
      .where(eq(subscribers.userId, req.session.userId));

    const total = totalResult?.count || 0;

    // Get active subscriber count
    const [activeResult] = await db
      .select({ count: count() })
      .from(subscribers)
      .where(
        and(
          eq(subscribers.userId, req.session.userId),
          eq(subscribers.status, 'active')
        )
      );

    const active = activeResult?.count || 0;

    // Get unsubscribed count
    const [unsubscribedResult] = await db
      .select({ count: count() })
      .from(subscribers)
      .where(
        and(
          eq(subscribers.userId, req.session.userId),
          eq(subscribers.status, 'unsubscribed')
        )
      );

    const unsubscribed = unsubscribedResult?.count || 0;

    return res.status(200).json({
      total,
      active,
      unsubscribed
    });
  } catch (error: any) {
    console.error('Error fetching subscriber stats:', error);
    return res.status(500).json({ 
      message: 'Server error', 
      error: error.message 
    });
  }
});

// Get all subscribers
router.get('/', async (req: Request, res: Response) => {
  try {
    if (!req.session?.userId) {
      return res.status(401).json({ message: 'Not authenticated' });
    }

    const { page = 1, limit = 20, status, listId } = req.query;
    const offset = (Number(page) - 1) * Number(limit);

    let whereConditions = and(eq(subscribers.userId, req.session.userId));
    
    // Apply filters if provided
    if (status) {
      whereConditions = and(whereConditions, eq(subscribers.status, status as string));
    }
    
    if (listId) {
      whereConditions = and(whereConditions, eq(subscribers.listId, Number(listId)));
    }
    
    const query = db
      .select()
      .from(subscribers)
      .where(whereConditions);

    const subscriberList = await query
      .limit(Number(limit))
      .offset(offset)
      .orderBy(subscribers.createdAt);

    // Get total count for pagination
    const [countResult] = await db
      .select({ count: count() })
      .from(subscribers)
      .where(eq(subscribers.userId, req.session.userId));

    const total = countResult?.count || 0;

    return res.status(200).json({
      data: subscriberList,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total,
        pages: Math.ceil(total / Number(limit)),
      }
    });
  } catch (error: any) {
    console.error('Error fetching subscribers:', error);
    return res.status(500).json({ 
      message: 'Server error', 
      error: error.message 
    });
  }
});

// Create a new subscriber
router.post('/', async (req: Request, res: Response) => {
  try {
    if (!req.session?.userId) {
      return res.status(401).json({ message: 'Not authenticated' });
    }

    // Validate the request body
    const validationResult = subscriberSchema.safeParse(req.body);
    if (!validationResult.success) {
      return res.status(400).json({ 
        message: 'Invalid data', 
        errors: validationResult.error.errors 
      });
    }

    const { email, name, status = 'active', source, listId, metadata } = validationResult.data;

    // Check if subscriber already exists
    const [existingSubscriber] = await db
      .select()
      .from(subscribers)
      .where(
        and(
          eq(subscribers.userId, req.session.userId),
          eq(subscribers.email, email)
        )
      );

    if (existingSubscriber) {
      return res.status(400).json({ message: 'Subscriber with this email already exists' });
    }

    // Create the subscriber
    const [newSubscriber] = await db
      .insert(subscribers)
      .values({
        userId: req.session.userId,
        email,
        name,
        status,
        source,
        listId,
        metadata,
      })
      .returning();

    return res.status(201).json({
      message: 'Subscriber created successfully',
      subscriber: newSubscriber,
    });
  } catch (error: any) {
    console.error('Error creating subscriber:', error);
    return res.status(500).json({ 
      message: 'Server error', 
      error: error.message 
    });
  }
});

// Update a subscriber
router.patch('/:id', async (req: Request, res: Response) => {
  try {
    if (!req.session?.userId) {
      return res.status(401).json({ message: 'Not authenticated' });
    }

    const { id } = req.params;

    // Validate the request body
    const validationResult = subscriberSchema.partial().safeParse(req.body);
    if (!validationResult.success) {
      return res.status(400).json({ 
        message: 'Invalid data', 
        errors: validationResult.error.errors 
      });
    }

    const { email, name, status, source, listId, metadata } = validationResult.data;

    // Check if subscriber exists and belongs to user
    const [existingSubscriber] = await db
      .select()
      .from(subscribers)
      .where(
        and(
          eq(subscribers.id, Number(id)),
          eq(subscribers.userId, req.session.userId)
        )
      );

    if (!existingSubscriber) {
      return res.status(404).json({ message: 'Subscriber not found' });
    }

    // Update the subscriber
    const [updatedSubscriber] = await db
      .update(subscribers)
      .set({
        email: email || existingSubscriber.email,
        name: name !== undefined ? name : existingSubscriber.name,
        status: status || existingSubscriber.status,
        source: source || existingSubscriber.source,
        listId: listId !== undefined ? listId : existingSubscriber.listId,
        metadata: metadata || existingSubscriber.metadata,
        updatedAt: new Date(),
      })
      .where(
        and(
          eq(subscribers.id, Number(id)),
          eq(subscribers.userId, req.session.userId)
        )
      )
      .returning();

    return res.status(200).json({
      message: 'Subscriber updated successfully',
      subscriber: updatedSubscriber,
    });
  } catch (error: any) {
    console.error('Error updating subscriber:', error);
    return res.status(500).json({ 
      message: 'Server error', 
      error: error.message 
    });
  }
});

// Delete a subscriber
router.delete('/:id', async (req: Request, res: Response) => {
  try {
    if (!req.session?.userId) {
      return res.status(401).json({ message: 'Not authenticated' });
    }

    const { id } = req.params;

    // Check if subscriber exists and belongs to user
    const [existingSubscriber] = await db
      .select()
      .from(subscribers)
      .where(
        and(
          eq(subscribers.id, Number(id)),
          eq(subscribers.userId, req.session.userId)
        )
      );

    if (!existingSubscriber) {
      return res.status(404).json({ message: 'Subscriber not found' });
    }

    // Delete the subscriber
    await db
      .delete(subscribers)
      .where(
        and(
          eq(subscribers.id, Number(id)),
          eq(subscribers.userId, req.session.userId)
        )
      );

    return res.status(200).json({
      message: 'Subscriber deleted successfully',
    });
  } catch (error: any) {
    console.error('Error deleting subscriber:', error);
    return res.status(500).json({ 
      message: 'Server error', 
      error: error.message 
    });
  }
});

// Get all subscriber lists
router.get('/lists', async (req: Request, res: Response) => {
  try {
    if (!req.session?.userId) {
      return res.status(401).json({ message: 'Not authenticated' });
    }

    const lists = await db
      .select()
      .from(subscriberLists)
      .where(eq(subscriberLists.userId, req.session.userId));

    return res.status(200).json(lists);
  } catch (error: any) {
    console.error('Error fetching subscriber lists:', error);
    return res.status(500).json({ 
      message: 'Server error', 
      error: error.message 
    });
  }
});

// Create a new subscriber list
router.post('/lists', async (req: Request, res: Response) => {
  try {
    if (!req.session?.userId) {
      return res.status(401).json({ message: 'Not authenticated' });
    }

    // Validate the request body
    const validationResult = subscriberListSchema.safeParse(req.body);
    if (!validationResult.success) {
      return res.status(400).json({ 
        message: 'Invalid data', 
        errors: validationResult.error.errors 
      });
    }

    const { name, description } = validationResult.data;

    // Create the list
    const [newList] = await db
      .insert(subscriberLists)
      .values({
        userId: req.session.userId,
        name,
        description,
      })
      .returning();

    return res.status(201).json({
      message: 'Subscriber list created successfully',
      list: newList,
    });
  } catch (error: any) {
    console.error('Error creating subscriber list:', error);
    return res.status(500).json({ 
      message: 'Server error', 
      error: error.message 
    });
  }
});

// Import subscribers from CSV
router.post('/import', upload.single('file'), async (req: Request, res: Response) => {
  try {
    if (!req.session?.userId) {
      return res.status(401).json({ message: 'Not authenticated' });
    }

    if (!req.file) {
      return res.status(400).json({ message: 'No file uploaded' });
    }

    const filePath = req.file.path;
    const listId = req.body.listId ? Number(req.body.listId) : undefined;

    // Parse CSV file
    const parsedSubscribers: any[] = [];
    const parser = fs
      .createReadStream(filePath)
      .pipe(parse({
        columns: true,
        skip_empty_lines: true,
      }));

    for await (const record of parser) {
      // Validate email
      if (!record.email || !z.string().email().safeParse(record.email).success) {
        continue; // Skip records without valid email
      }

      parsedSubscribers.push({
        userId: req.session.userId,
        email: record.email,
        name: record.name || null,
        status: record.status === 'unsubscribed' ? 'unsubscribed' : 'active',
        source: 'import',
        listId,
        metadata: {},
      });
    }

    // Clean up the uploaded file
    fs.unlinkSync(filePath);

    if (parsedSubscribers.length === 0) {
      return res.status(400).json({ message: 'No valid subscribers found in the CSV file' });
    }

    // Insert parsed subscribers
    const subscribersToInsert = parsedSubscribers.map(sub => ({
      userId: req.session.userId,
      email: sub.email,
      name: sub.name || null,
      status: (sub.status || 'active') as 'active' | 'inactive' | 'unsubscribed',
      source: 'csv_import',
      listId: listId || null,
      metadata: sub.metadata || {}
    }));
    
    const result = await db
      .insert(subscribers)
      .values(subscribersToInsert)
      .onConflictDoNothing() // Skip existing emails
      .returning();

    // Handle the result safely to accommodate different return types
    const importedCount = Array.isArray(result) ? result.length : 0;
    
    return res.status(200).json({
      message: `${importedCount} subscribers imported successfully`,
      imported: importedCount,
      total: subscribers.length,
    });
  } catch (error: any) {
    console.error('Error importing subscribers:', error);
    return res.status(500).json({ 
      message: 'Server error', 
      error: error.message 
    });
  }
});

// Export subscribers to CSV
router.get('/export', async (req: Request, res: Response) => {
  try {
    if (!req.session?.userId) {
      return res.status(401).json({ message: 'Not authenticated' });
    }

    const { listId, status } = req.query;

    // Query subscribers based on filters
    let whereConditions = and(eq(subscribers.userId, req.session.userId));
    
    // Apply filters if provided
    if (status) {
      whereConditions = and(whereConditions, eq(subscribers.status, status as string));
    }
    
    if (listId) {
      whereConditions = and(whereConditions, eq(subscribers.listId, Number(listId)));
    }
    
    const query = db
      .select()
      .from(subscribers)
      .where(whereConditions);

    const subscriberList = await query;

    if (subscriberList.length === 0) {
      return res.status(404).json({ message: 'No subscribers found to export' });
    }

    // Create CSV
    const filename = `subscribers_export_${Date.now()}.csv`;
    const filePath = path.join('uploads', filename);

    const csvWriter = createObjectCsvWriter({
      path: filePath,
      header: [
        { id: 'email', title: 'Email' },
        { id: 'name', title: 'Name' },
        { id: 'status', title: 'Status' },
        { id: 'source', title: 'Source' },
        { id: 'createdAt', title: 'Created At' },
      ],
    });

    await csvWriter.writeRecords(subscriberList);

    // Send file for download
    res.download(filePath, filename, (err) => {
      if (err) {
        console.error('Error downloading CSV:', err);
      }
      // Clean up the file after sending
      fs.unlink(filePath, (unlinkErr) => {
        if (unlinkErr) {
          console.error('Error deleting temporary CSV file:', unlinkErr);
        }
      });
    });
  } catch (error: any) {
    console.error('Error exporting subscribers:', error);
    return res.status(500).json({ 
      message: 'Server error', 
      error: error.message 
    });
  }
});

export default router;