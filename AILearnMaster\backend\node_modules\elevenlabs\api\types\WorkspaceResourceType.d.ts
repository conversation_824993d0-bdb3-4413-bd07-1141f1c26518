/**
 * This file was auto-generated by Fern from our API Definition.
 */
/**
 * Resource types that can be shared in the workspace. The name always need to match the collection names
 */
export type WorkspaceResourceType = "voice" | "voice_collection" | "pronunciation_dictionary" | "dubbing" | "project" | "convai_agents" | "convai_knowledge_base_documents" | "convai_tools" | "convai_settings" | "convai_secrets" | "music_latent" | "convai_phone_numbers" | "convai_mcps" | "convai_batch_calls";
export declare const WorkspaceResourceType: {
    readonly Voice: "voice";
    readonly VoiceCollection: "voice_collection";
    readonly PronunciationDictionary: "pronunciation_dictionary";
    readonly Dubbing: "dubbing";
    readonly Project: "project";
    readonly ConvaiAgents: "convai_agents";
    readonly ConvaiKnowledgeBaseDocuments: "convai_knowledge_base_documents";
    readonly ConvaiTools: "convai_tools";
    readonly ConvaiSettings: "convai_settings";
    readonly ConvaiSecrets: "convai_secrets";
    readonly MusicLatent: "music_latent";
    readonly ConvaiPhoneNumbers: "convai_phone_numbers";
    readonly ConvaiMcps: "convai_mcps";
    readonly ConvaiBatchCalls: "convai_batch_calls";
};
