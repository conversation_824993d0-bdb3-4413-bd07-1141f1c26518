/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as ElevenLabs from "../index";
export interface ResourceMetadataResponseModel {
    /** The ID of the resource. */
    resource_id: string;
    /** The type of the resource. */
    resource_type: ElevenLabs.WorkspaceResourceType;
    /** The ID of the user who created the resource. */
    creator_user_id?: string;
    /** A mapping of roles to group IDs. When the resource is shared with a user, the group id is the user's id. */
    role_to_group_ids: Record<string, string[]>;
    /** List of options for sharing the resource further in the workspace. These are users who don't have access to the resource yet. */
    share_options: ElevenLabs.ShareOptionResponseModel[];
}
