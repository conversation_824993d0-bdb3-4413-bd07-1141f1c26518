/**
 * This file was auto-generated by <PERSON><PERSON> from our API Definition.
 */
import * as ElevenLabs from "../index";
export interface PromptAgent {
    /** The prompt for the agent */
    prompt?: string;
    /** The LLM to query with the prompt and the chat history */
    llm?: ElevenLabs.Llm;
    /** The temperature for the LLM */
    temperature?: number;
    /** If greater than 0, maximum number of tokens the LLM can predict */
    max_tokens?: number;
    /** A list of tools that the agent can use over the course of the conversation */
    tools?: ElevenLabs.PromptAgentOutputToolsItem[];
    /** A list of IDs of tools used by the agent */
    tool_ids?: string[];
    /** A list of MCP server ids to be used by the agent */
    mcp_server_ids?: string[];
    /** A list of knowledge bases to be used by the agent */
    knowledge_base?: ElevenLabs.KnowledgeBaseLocator[];
    /** Definition for a custom LLM if LLM field is set to 'CUSTOM_LLM' */
    custom_llm?: ElevenLabs.CustomLlm;
    /** Whether to ignore the default personality */
    ignore_default_personality?: boolean;
    /** Configuration for RAG */
    rag?: ElevenLabs.RagConfig;
}
