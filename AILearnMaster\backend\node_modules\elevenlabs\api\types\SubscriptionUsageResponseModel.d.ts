/**
 * This file was auto-generated by Fern from our API Definition.
 */
export interface SubscriptionUsageResponseModel {
    /** The rollover credits quota. */
    rollover_credits_quota: number;
    /** The subscription cycle credits quota. */
    subscription_cycle_credits_quota: number;
    /** The manually gifted credits quota. */
    manually_gifted_credits_quota: number;
    /** The rollover credits used. */
    rollover_credits_used: number;
    /** The subscription cycle credits used. */
    subscription_cycle_credits_used: number;
    /** The manually gifted credits used. */
    manually_gifted_credits_used: number;
    /** The paid usage based credits used. */
    paid_usage_based_credits_used: number;
    /** The actual reported credits. */
    actual_reported_credits: number;
}
