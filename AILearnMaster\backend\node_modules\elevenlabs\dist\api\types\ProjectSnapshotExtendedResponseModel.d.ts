/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as ElevenLabs from "../index";
export interface ProjectSnapshotExtendedResponseModel {
    /** The ID of the project snapshot. */
    project_snapshot_id: string;
    /** The ID of the project. */
    project_id: string;
    /** The creation date of the project snapshot. */
    created_at_unix: number;
    /** The name of the project snapshot. */
    name: string;
    /** (Deprecated) */
    audio_upload?: Record<string, unknown>;
    /** (Deprecated) */
    zip_upload?: Record<string, unknown>;
    character_alignments: ElevenLabs.CharacterAlignmentModel[];
}
