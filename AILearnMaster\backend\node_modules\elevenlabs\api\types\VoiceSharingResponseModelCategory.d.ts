/**
 * This file was auto-generated by Fern from our API Definition.
 */
/**
 * The category of the voice.
 */
export type VoiceSharingResponseModelCategory = "generated" | "cloned" | "premade" | "professional" | "famous" | "high_quality";
export declare const VoiceSharingResponseModelCategory: {
    readonly Generated: "generated";
    readonly Cloned: "cloned";
    readonly Premade: "premade";
    readonly Professional: "professional";
    readonly Famous: "famous";
    readonly HighQuality: "high_quality";
};
